import './polyfills.server.mjs';
import{a as J,b as U,c as W,d as G,e as K}from"./chunk-YNGSLRF7.mjs";import{a as vt,b as ft}from"./chunk-CHXIZKOG.mjs";import"./chunk-R2YOBFRV.mjs";import{a as St}from"./chunk-46RSLVWY.mjs";import{a as ht}from"./chunk-GSUXRCMW.mjs";import{a as z,b as q}from"./chunk-5V5SXYU2.mjs";import{a as at,b as ot,c as mt,d as lt,e as rt,f as ct,g as dt,h as pt,i as _t,j as st,l as Ct}from"./chunk-CUT3Q574.mjs";import{a as ut,b as gt}from"./chunk-S2DPCQFA.mjs";import{a as yt,b as xt}from"./chunk-4QI52JLP.mjs";import{a as Q,b as X,c as Y,d as Z,e as tt,f as et,g as it,h as nt}from"./chunk-PWFZPCXU.mjs";import"./chunk-I44XXN6Y.mjs";import"./chunk-A3ICGYXF.mjs";import{_ as H,aa as j,ba as N,ca as $,da as F}from"./chunk-N6IFEGQ2.mjs";import{b as R,e as V,i as O}from"./chunk-46VHTMVE.mjs";import{B as S,Fb as a,Gb as d,Gc as A,Hb as E,J as b,Jc as B,Ka as o,La as f,Lb as k,Lc as P,Qb as L,Wb as I,Yb as M,ab as p,cb as l,ha as w,l as h,lb as n,mb as i,nb as _,oa as y,ob as C,pa as x,pb as v,rb as D,ub as u,v as T,wb as c}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";function Et(t,m){t&1&&(n(0,"div",3),_(1,"mat-spinner",4),i())}function It(t,m){if(t&1&&(n(0,"mat-card-subtitle"),a(1),i()),t&2){let e=c(2);o(),E("Code: ",e.company.code,"")}}function Mt(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Description"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),d(e.company.description)}}function Tt(t,m){t&1&&_(0,"mat-divider")}function bt(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Address"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),d(e.company.address)}}function wt(t,m){t&1&&_(0,"mat-divider")}function kt(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Location"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),k(" ",e.company.city,"",e.company.city&&(e.company.state||e.company.country)?", ":""," ",e.company.state,"",e.company.state&&e.company.country?", ":""," ",e.company.country," ")}}function Lt(t,m){t&1&&_(0,"mat-divider")}function At(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Postal Code"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),d(e.company.postalCode)}}function Bt(t,m){t&1&&_(0,"mat-divider")}function Pt(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Phone"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),d(e.company.phone)}}function Rt(t,m){t&1&&_(0,"mat-divider")}function Vt(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Email"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),d(e.company.email)}}function Ot(t,m){t&1&&_(0,"mat-divider")}function Ht(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Website"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),d(e.company.website)}}function jt(t,m){t&1&&_(0,"mat-divider")}function Nt(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Tax Number"),i(),n(3,"span",9),a(4),i()()),t&2){let e=c(2);o(4),d(e.company.taxNumber)}}function $t(t,m){t&1&&_(0,"mat-divider")}function Ft(t,m){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Last Updated"),i(),n(3,"span",9),a(4),I(5,"date"),i()()),t&2){let e=c(2);o(4),d(M(5,1,e.company.updatedAt,"medium"))}}function zt(t,m){t&1&&(n(0,"th",27),a(1,"Name"),i())}function qt(t,m){if(t&1&&(n(0,"td",28),a(1),i()),t&2){let e=m.$implicit;o(),d(e.name)}}function Jt(t,m){t&1&&(n(0,"th",27),a(1,"Code"),i())}function Ut(t,m){if(t&1&&(n(0,"td",28),a(1),i()),t&2){let e=m.$implicit;o(),d(e.code)}}function Wt(t,m){t&1&&(n(0,"th",27),a(1,"City"),i())}function Gt(t,m){if(t&1&&(n(0,"td",28),a(1),i()),t&2){let e=m.$implicit;o(),d(e.city)}}function Kt(t,m){t&1&&(n(0,"th",27),a(1,"Country"),i())}function Qt(t,m){if(t&1&&(n(0,"td",28),a(1),i()),t&2){let e=m.$implicit;o(),d(e.country)}}function Xt(t,m){t&1&&(n(0,"th",27),a(1,"Status"),i())}function Yt(t,m){if(t&1&&(n(0,"td",28)(1,"mat-icon",29),a(2),i()()),t&2){let e=m.$implicit;o(),l("color",e.isActive?"primary":"warn"),o(),E(" ",e.isActive?"check_circle":"cancel"," ")}}function Zt(t,m){t&1&&(n(0,"th",27),a(1,"Stores"),i())}function te(t,m){if(t&1&&(n(0,"td",28),a(1),i()),t&2){let e=m.$implicit;o(),d(e.storesCount)}}function ee(t,m){t&1&&(n(0,"th",27),a(1,"Actions"),i())}function ie(t,m){if(t&1){let e=D();n(0,"td",28)(1,"button",30),u("click",function(){let s=y(e).$implicit,g=c(3);return x(g.viewLocation(s.id))}),n(2,"mat-icon"),a(3,"visibility"),i()()()}}function ne(t,m){t&1&&_(0,"tr",31)}function ae(t,m){t&1&&_(0,"tr",32)}function oe(t,m){if(t&1&&(n(0,"div")(1,"table",15),C(2,16),p(3,zt,2,0,"th",17)(4,qt,2,1,"td",18),v(),C(5,19),p(6,Jt,2,0,"th",17)(7,Ut,2,1,"td",18),v(),C(8,20),p(9,Wt,2,0,"th",17)(10,Gt,2,1,"td",18),v(),C(11,21),p(12,Kt,2,0,"th",17)(13,Qt,2,1,"td",18),v(),C(14,22),p(15,Xt,2,0,"th",17)(16,Yt,3,2,"td",18),v(),C(17,23),p(18,Zt,2,0,"th",17)(19,te,2,1,"td",18),v(),C(20,24),p(21,ee,2,0,"th",17)(22,ie,4,0,"td",18),v(),p(23,ne,1,0,"tr",25)(24,ae,1,0,"tr",26),i()()),t&2){let e=c(2);o(),l("dataSource",e.locations),o(22),l("matHeaderRowDef",e.locationColumns),o(),l("matRowDefColumns",e.locationColumns)}}function me(t,m){t&1&&(n(0,"div",33),a(1,' No locations found for this company. Click "Add Location" to create one. '),i())}function le(t,m){if(t&1){let e=D();n(0,"div")(1,"mat-card")(2,"mat-card-header")(3,"div",5)(4,"mat-icon",6),a(5,"business"),i()(),n(6,"mat-card-title"),a(7),i(),p(8,It,2,1,"mat-card-subtitle",2),i(),n(9,"mat-card-content")(10,"div",7)(11,"h3"),a(12,"Company Details"),i(),n(13,"mat-list"),p(14,Mt,5,1,"mat-list-item",2)(15,Tt,1,0,"mat-divider",2)(16,bt,5,1,"mat-list-item",2)(17,wt,1,0,"mat-divider",2)(18,kt,5,5,"mat-list-item",2)(19,Lt,1,0,"mat-divider",2)(20,At,5,1,"mat-list-item",2)(21,Bt,1,0,"mat-divider",2)(22,Pt,5,1,"mat-list-item",2)(23,Rt,1,0,"mat-divider",2)(24,Vt,5,1,"mat-list-item",2)(25,Ot,1,0,"mat-divider",2)(26,Ht,5,1,"mat-list-item",2)(27,jt,1,0,"mat-divider",2)(28,Nt,5,1,"mat-list-item",2)(29,$t,1,0,"mat-divider",2),n(30,"mat-list-item")(31,"span",8),a(32,"Status"),i(),n(33,"span",9),a(34),i()(),_(35,"mat-divider"),n(36,"mat-list-item")(37,"span",8),a(38,"Created At"),i(),n(39,"span",9),a(40),I(41,"date"),i()(),_(42,"mat-divider"),p(43,Ft,6,4,"mat-list-item",2),i()(),n(44,"div",10)(45,"h3"),a(46,"Locations"),i(),n(47,"div",11)(48,"button",12),u("click",function(){y(e);let s=c();return x(s.addLocation())}),n(49,"mat-icon"),a(50,"add"),i(),a(51," Add Location "),i()(),p(52,oe,25,3,"div",2)(53,me,2,0,"div",13),i()(),n(54,"mat-card-actions")(55,"button",12),u("click",function(){y(e);let s=c();return x(s.editCompany())}),n(56,"mat-icon"),a(57,"edit"),i(),a(58," Edit "),i(),n(59,"button",14),u("click",function(){y(e);let s=c();return x(s.goBack())}),n(60,"mat-icon"),a(61,"arrow_back"),i(),a(62," Back "),i()()()()}if(t&2){let e=c();o(7),d(e.company.name),o(),l("ngIf",e.company.code),o(6),l("ngIf",e.company.description),o(),l("ngIf",e.company.description),o(),l("ngIf",e.company.address),o(),l("ngIf",e.company.address),o(),l("ngIf",e.company.city||e.company.state||e.company.country),o(),l("ngIf",e.company.city||e.company.state||e.company.country),o(),l("ngIf",e.company.postalCode),o(),l("ngIf",e.company.postalCode),o(),l("ngIf",e.company.phone),o(),l("ngIf",e.company.phone),o(),l("ngIf",e.company.email),o(),l("ngIf",e.company.email),o(),l("ngIf",e.company.website),o(),l("ngIf",e.company.website),o(),l("ngIf",e.company.taxNumber),o(),l("ngIf",e.company.taxNumber),o(5),d(e.company.isActive?"Active":"Inactive"),o(6),d(M(41,23,e.company.createdAt,"medium")),o(3),l("ngIf",e.company.updatedAt),o(9),l("ngIf",e.locations.length>0),o(),l("ngIf",e.locations.length===0)}}var Re=(()=>{class t{constructor(e,r,s,g,Dt){this.route=e,this.router=r,this.companyService=s,this.locationService=g,this.snackBar=Dt,this.company=null,this.locations=[],this.isLoading=!1,this.locationColumns=["name","code","city","country","isActive","storesCount","actions"]}ngOnInit(){this.loadCompanyData()}loadCompanyData(){let e=this.route.snapshot.paramMap.get("id");if(!e){this.snackBar.open("Company ID is missing","Close",{duration:3e3}),this.router.navigate(["/companies"]);return}this.isLoading=!0,T({company:this.companyService.getById(+e).pipe(S(r=>(console.error("Error loading company:",r),this.snackBar.open("Failed to load company details","Close",{duration:3e3}),h(null)))),locations:this.locationService.getByCompany(+e).pipe(S(r=>(console.error("Error loading locations:",r),h([]))))}).pipe(b(()=>this.isLoading=!1)).subscribe(r=>{this.company=r.company,this.locations=r.locations,this.company||this.router.navigate(["/companies"])})}goBack(){this.router.navigate(["/companies"])}editCompany(){this.company&&this.router.navigate(["/companies/edit",this.company.id])}viewLocation(e){this.router.navigate(["/locations",e])}addLocation(){this.company&&this.router.navigate(["/locations/new"],{queryParams:{companyId:this.company.id}})}static{this.\u0275fac=function(r){return new(r||t)(f(R),f(V),f(ht),f(St),f(yt))}}static{this.\u0275cmp=w({type:t,selectors:[["app-company-detail"]],standalone:!0,features:[L],decls:3,vars:2,consts:[[1,"container"],["class","loading-shade",4,"ngIf"],[4,"ngIf"],[1,"loading-shade"],["diameter","50"],["mat-card-avatar",""],["color","primary"],[1,"tab-content"],["matListItemTitle",""],["matListItemLine",""],[1,"tab-content","mt-4"],[1,"action-bar"],["mat-raised-button","","color","primary",3,"click"],["class","no-data-message",4,"ngIf"],["mat-button","",3,"click"],["mat-table","",1,"full-width",3,"dataSource"],["matColumnDef","name"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","code"],["matColumnDef","city"],["matColumnDef","country"],["matColumnDef","isActive"],["matColumnDef","storesCount"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["mat-header-cell",""],["mat-cell",""],[3,"color"],["mat-icon-button","","color","primary","matTooltip","View",3,"click"],["mat-header-row",""],["mat-row",""],[1,"no-data-message"]],template:function(r,s){r&1&&(n(0,"div",0),p(1,Et,2,0,"div",1)(2,le,63,26,"div",2),i()),r&2&&(o(),l("ngIf",s.isLoading),o(),l("ngIf",s.company))},dependencies:[P,A,B,O,N,H,j,nt,Q,tt,it,Y,et,Z,X,F,$,gt,ut,xt,q,z,K,W,G,U,J,Ct,at,mt,dt,lt,ot,pt,rt,ct,_t,st,ft,vt],styles:[".container[_ngcontent-%COMP%]{padding:20px;position:relative;min-height:400px}.loading-shade[_ngcontent-%COMP%]{position:absolute;inset:0;background:#00000026;z-index:1;display:flex;align-items:center;justify-content:center}mat-card[_ngcontent-%COMP%]{max-width:1000px;margin:0 auto}mat-card-actions[_ngcontent-%COMP%]{padding:16px;display:flex;gap:10px}mat-list-item[_ngcontent-%COMP%]{height:auto!important;padding:16px 0}.tab-content[_ngcontent-%COMP%]{padding:20px 0}.action-bar[_ngcontent-%COMP%]{margin-bottom:20px;display:flex;justify-content:flex-end}.full-width[_ngcontent-%COMP%]{width:100%}.no-data-message[_ngcontent-%COMP%]{text-align:center;padding:20px;font-style:italic;color:#0000008a}"]})}}return t})();export{Re as CompanyDetailComponent};
