using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class StockController : ApiControllerBase
{
    private readonly IStockService _stockService;
    private readonly ILogger<StockController> _logger;

    public StockController(IStockService stockService, ILogger<StockController> logger)
    {
        _stockService = stockService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<StockOnHandDto>>> GetAll()
    {
        try
        {
            var stock = await _stockService.GetStockOnHandAsync();
            return Ok(stock);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all stock");
            return StatusCode(500, "An error occurred while retrieving stock information");
        }
    }

    [HttpGet("product/{productId}")]
    public async Task<ActionResult<IEnumerable<StockOnHandDto>>> GetByProductId(int productId)
    {
        try
        {
            var stock = await _stockService.GetStockOnHandByProductIdAsync(productId);
            return Ok(stock);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stock for product {ProductId}", productId);
            return StatusCode(500, "An error occurred while retrieving stock information");
        }
    }

    [HttpGet("costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockOnHandDto>>> GetByCostCenterId(int costCenterId)
    {
        try
        {
            var stock = await _stockService.GetStockOnHandByCostCenterIdAsync(costCenterId);
            return Ok(stock);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stock for cost center {CostCenterId}", costCenterId);
            return StatusCode(500, "An error occurred while retrieving stock information");
        }
    }

    [HttpGet("item")]
    public async Task<ActionResult<StockOnHandDto>> GetStockItem(
        [FromQuery] int productId,
        [FromQuery] int costCenterId)
    {
        try
        {
            var stock = await _stockService.GetStockOnHandAsync(productId, costCenterId);
            if (stock == null)
                return NotFound();

            return Ok(stock);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stock item for product {ProductId}, cost center {CostCenterId}",
                productId, costCenterId);
            return StatusCode(500, "An error occurred while retrieving stock information");
        }
    }

    [HttpGet("summary/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<StockOnHandSummaryDto>>> GetSummary(int costCenterId)
    {
        var summary = await _stockService.GetStockSummaryByCostCenterIdAsync(costCenterId);
        return Ok(summary);
    }

    [HttpGet("lowstock")]
    public async Task<ActionResult<IEnumerable<StockOnHandSummaryDto>>> GetLowStock([FromQuery] int? costCenterId = null)
    {
        var lowStock = await _stockService.GetLowStockItemsAsync(costCenterId);
        return Ok(lowStock);
    }

    [HttpGet("overstock")]
    public async Task<ActionResult<IEnumerable<StockOnHandSummaryDto>>> GetOverStock([FromQuery] int? costCenterId = null)
    {
        var overStock = await _stockService.GetOverStockItemsAsync(costCenterId);
        return Ok(overStock);
    }

    [HttpGet("reorder")]
    public async Task<ActionResult<IEnumerable<StockOnHandSummaryDto>>> GetReorderItems([FromQuery] int? costCenterId = null)
    {
        var reorderItems = await _stockService.GetReorderItemsAsync(costCenterId);
        return Ok(reorderItems);
    }

    [HttpGet("expiring/{daysToExpiry}")]
    public async Task<ActionResult<IEnumerable<StockOnHandDto>>> GetExpiringStock(
        int daysToExpiry, 
        [FromQuery] int? costCenterId = null)
    {
        var expiringStock = await _stockService.GetExpiringStockAsync(daysToExpiry, costCenterId);
        return Ok(expiringStock);
    }

    [HttpPost("adjust")]
    public async Task<IActionResult> AdjustStock(StockAdjustmentDto stockAdjustmentDto)
    {
        await _stockService.AdjustStockAsync(stockAdjustmentDto);
        return NoContent();
    }
}
