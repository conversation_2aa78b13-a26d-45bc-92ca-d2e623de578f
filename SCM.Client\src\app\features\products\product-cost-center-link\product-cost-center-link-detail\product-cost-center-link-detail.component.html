<div class="page-container">
  <div class="page-header">
    <div class="header-content">
      <button mat-icon-button (click)="onCancel()" matTooltip="Back to list">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1>{{pageTitle}}</h1>
    </div>
  </div>

  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading...</p>
  </div>

  <form [formGroup]="linkForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
    <mat-card class="form-card">
      <mat-card-header>
        <mat-card-title>Link Details</mat-card-title>
        <mat-card-subtitle>Configure the product cost center link settings</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Product</mat-label>
            <mat-select formControlName="productId" [disabled]="isEditMode">
              <mat-option *ngFor="let product of products" [value]="product.id">
                {{getProductDisplayName(product)}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="linkForm.get('productId')?.hasError('required')">
              Product is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Cost Center</mat-label>
            <mat-select formControlName="costCenterId" [disabled]="isEditMode">
              <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                {{getCostCenterDisplayName(costCenter)}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="linkForm.get('costCenterId')?.hasError('required')">
              Cost Center is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Minimum Stock</mat-label>
            <input matInput type="number" formControlName="minStock" min="0" step="0.01">
            <mat-hint>Minimum stock level for this product in this cost center</mat-hint>
            <mat-error *ngIf="linkForm.get('minStock')?.hasError('min')">
              Minimum stock must be 0 or greater
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Maximum Stock</mat-label>
            <input matInput type="number" formControlName="maxStock" min="0" step="0.01">
            <mat-hint>Maximum stock level for this product in this cost center</mat-hint>
            <mat-error *ngIf="linkForm.get('maxStock')?.hasError('min')">
              Maximum stock must be 0 or greater
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Reorder Point</mat-label>
            <input matInput type="number" formControlName="reorderPoint" min="0" step="0.01">
            <mat-hint>Stock level at which to trigger reorder</mat-hint>
            <mat-error *ngIf="linkForm.get('reorderPoint')?.hasError('min')">
              Reorder point must be 0 or greater
            </mat-error>
          </mat-form-field>

          <div class="checkbox-container">
            <mat-checkbox formControlName="autoReorder">
              Auto Reorder
            </mat-checkbox>
            <div class="checkbox-hint">
              Automatically create reorder requests when stock falls below reorder point
            </div>
          </div>
        </div>

        <div *ngIf="linkForm.hasError('stockRangeInvalid')" class="form-error">
          <mat-icon>error</mat-icon>
          <span>Maximum stock must be greater than or equal to minimum stock</span>
        </div>
      </mat-card-content>

      <mat-card-actions align="end">
        <button mat-button type="button" (click)="onCancel()" [disabled]="isSaving">
          Cancel
        </button>
        <button mat-raised-button color="primary" type="submit" [disabled]="linkForm.invalid || isSaving">
          <mat-spinner diameter="20" *ngIf="isSaving"></mat-spinner>
          <span *ngIf="!isSaving">{{submitButtonText}}</span>
          <span *ngIf="isSaving">Saving...</span>
        </button>
      </mat-card-actions>
    </mat-card>
  </form>
</div>
