import './polyfills.server.mjs';
import{a as J,b as U,c as G,d as K,e as Q}from"./chunk-YNGSLRF7.mjs";import{a as ft,b as Ct}from"./chunk-CHXIZKOG.mjs";import"./chunk-R2YOBFRV.mjs";import{a as Et}from"./chunk-MY3IEP4E.mjs";import{a as Dt}from"./chunk-46RSLVWY.mjs";import{a as gt}from"./chunk-IIHLUASG.mjs";import{a as z,b as q}from"./chunk-5V5SXYU2.mjs";import{a as ot,b as at,c as lt,d as rt,e as mt,f as ct,g as dt,h as _t,i as st,j as pt,l as vt}from"./chunk-CUT3Q574.mjs";import{a as St,b as ht}from"./chunk-S2DPCQFA.mjs";import{a as xt,b as ut}from"./chunk-4QI52JLP.mjs";import{a as W,b as X,c as Y,d as Z,e as tt,f as et,g as it,h as nt}from"./chunk-PWFZPCXU.mjs";import"./chunk-I44XXN6Y.mjs";import"./chunk-A3ICGYXF.mjs";import{_ as R,aa as O,ba as H,ca as j,da as F}from"./chunk-N6IFEGQ2.mjs";import{b as B,e as $,i as N}from"./chunk-46VHTMVE.mjs";import{B as L,Fb as a,Gb as d,Gc as P,Hb as h,J as M,Jc as A,Ka as l,La as u,Lb as b,Lc as V,Qb as k,Wb as y,Yb as T,ab as m,cb as r,ha as w,l as g,lb as n,mb as i,nb as f,oa as C,ob as p,pa as x,pb as v,rb as E,ub as S,v as I,wb as _}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";function Tt(t,o){t&1&&(n(0,"div",3),f(1,"mat-spinner",4),i())}function It(t,o){if(t&1&&(n(0,"mat-card-subtitle"),a(1),i()),t&2){let e=_(2);l(),h("Code: ",e.location.code,"")}}function Mt(t,o){if(t&1){let e=E();n(0,"mat-list-item")(1,"span",8),a(2,"Company"),i(),n(3,"span",9)(4,"a",15),S("click",function(){C(e);let s=_(2);return x(s.goToCompany())}),a(5),i()()()}if(t&2){let e=_(2);l(5),d(e.location.companyName)}}function wt(t,o){t&1&&f(0,"mat-divider")}function bt(t,o){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Description"),i(),n(3,"span",9),a(4),i()()),t&2){let e=_(2);l(4),d(e.location.description)}}function kt(t,o){t&1&&f(0,"mat-divider")}function Pt(t,o){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Address"),i(),n(3,"span",9),a(4),i()()),t&2){let e=_(2);l(4),d(e.location.address)}}function At(t,o){t&1&&f(0,"mat-divider")}function Vt(t,o){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Location"),i(),n(3,"span",9),a(4),i()()),t&2){let e=_(2);l(4),b(" ",e.location.city,"",e.location.city&&(e.location.state||e.location.country)?", ":""," ",e.location.state,"",e.location.state&&e.location.country?", ":""," ",e.location.country," ")}}function Bt(t,o){t&1&&f(0,"mat-divider")}function $t(t,o){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Postal Code"),i(),n(3,"span",9),a(4),i()()),t&2){let e=_(2);l(4),d(e.location.postalCode)}}function Nt(t,o){t&1&&f(0,"mat-divider")}function Rt(t,o){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Phone"),i(),n(3,"span",9),a(4),i()()),t&2){let e=_(2);l(4),d(e.location.phone)}}function Ot(t,o){t&1&&f(0,"mat-divider")}function Ht(t,o){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Email"),i(),n(3,"span",9),a(4),i()()),t&2){let e=_(2);l(4),d(e.location.email)}}function jt(t,o){t&1&&f(0,"mat-divider")}function Ft(t,o){if(t&1&&(n(0,"mat-list-item")(1,"span",8),a(2,"Last Updated"),i(),n(3,"span",9),a(4),y(5,"date"),i()()),t&2){let e=_(2);l(4),d(T(5,1,e.location.updatedAt,"medium"))}}function zt(t,o){t&1&&(n(0,"th",29),a(1,"Name"),i())}function qt(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.name)}}function Jt(t,o){t&1&&(n(0,"th",29),a(1,"Code"),i())}function Ut(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.code)}}function Gt(t,o){t&1&&(n(0,"th",29),a(1,"City"),i())}function Kt(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.city)}}function Qt(t,o){t&1&&(n(0,"th",29),a(1,"Country"),i())}function Wt(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.country)}}function Xt(t,o){t&1&&(n(0,"th",29),a(1,"Sales Point"),i())}function Yt(t,o){if(t&1&&(n(0,"td",30)(1,"mat-icon",31),a(2),i()()),t&2){let e=o.$implicit;l(),r("color",e.isSalesPoint?"primary":"warn"),l(),h(" ",e.isSalesPoint?"check_circle":"cancel"," ")}}function Zt(t,o){t&1&&(n(0,"th",29),a(1,"Status"),i())}function te(t,o){if(t&1&&(n(0,"td",30)(1,"mat-icon",31),a(2),i()()),t&2){let e=o.$implicit;l(),r("color",e.isActive?"primary":"warn"),l(),h(" ",e.isActive?"check_circle":"cancel"," ")}}function ee(t,o){t&1&&(n(0,"th",29),a(1,"Cost Centers"),i())}function ie(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.costCentersCount)}}function ne(t,o){t&1&&(n(0,"th",29),a(1,"Actions"),i())}function oe(t,o){if(t&1){let e=E();n(0,"td",30)(1,"button",32),S("click",function(){let s=C(e).$implicit,D=_(3);return x(D.viewStore(s.id))}),n(2,"mat-icon"),a(3,"visibility"),i()()()}}function ae(t,o){t&1&&f(0,"tr",33)}function le(t,o){t&1&&f(0,"tr",34)}function re(t,o){if(t&1&&(n(0,"div")(1,"table",16),p(2,17),m(3,zt,2,0,"th",18)(4,qt,2,1,"td",19),v(),p(5,20),m(6,Jt,2,0,"th",18)(7,Ut,2,1,"td",19),v(),p(8,21),m(9,Gt,2,0,"th",18)(10,Kt,2,1,"td",19),v(),p(11,22),m(12,Qt,2,0,"th",18)(13,Wt,2,1,"td",19),v(),p(14,23),m(15,Xt,2,0,"th",18)(16,Yt,3,2,"td",19),v(),p(17,24),m(18,Zt,2,0,"th",18)(19,te,3,2,"td",19),v(),p(20,25),m(21,ee,2,0,"th",18)(22,ie,2,1,"td",19),v(),p(23,26),m(24,ne,2,0,"th",18)(25,oe,4,0,"td",19),v(),m(26,ae,1,0,"tr",27)(27,le,1,0,"tr",28),i()()),t&2){let e=_(2);l(),r("dataSource",e.stores),l(25),r("matHeaderRowDef",e.storeColumns),l(),r("matRowDefColumns",e.storeColumns)}}function me(t,o){t&1&&(n(0,"div",35),a(1,' No stores found for this location. Click "Add Store" to create one. '),i())}function ce(t,o){t&1&&(n(0,"th",29),a(1,"Name"),i())}function de(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.name)}}function _e(t,o){t&1&&(n(0,"th",29),a(1,"Code"),i())}function se(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.code)}}function pe(t,o){t&1&&(n(0,"th",29),a(1,"Store"),i())}function ve(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.storeName)}}function fe(t,o){t&1&&(n(0,"th",29),a(1,"Type"),i())}function Ce(t,o){if(t&1&&(n(0,"td",30),a(1),i()),t&2){let e=o.$implicit;l(),d(e.typeName)}}function xe(t,o){t&1&&(n(0,"th",29),a(1,"Sales Point"),i())}function ue(t,o){if(t&1&&(n(0,"td",30)(1,"mat-icon",31),a(2),i()()),t&2){let e=o.$implicit;l(),r("color",e.isSalesPoint?"primary":"warn"),l(),h(" ",e.isSalesPoint?"check_circle":"cancel"," ")}}function Se(t,o){t&1&&(n(0,"th",29),a(1,"Status"),i())}function he(t,o){if(t&1&&(n(0,"td",30)(1,"mat-icon",31),a(2),i()()),t&2){let e=o.$implicit;l(),r("color",e.isActive?"primary":"warn"),l(),h(" ",e.isActive?"check_circle":"cancel"," ")}}function De(t,o){t&1&&(n(0,"th",29),a(1,"Actions"),i())}function Ee(t,o){if(t&1){let e=E();n(0,"td",30)(1,"button",32),S("click",function(){let s=C(e).$implicit,D=_(3);return x(D.viewCostCenter(s.id))}),n(2,"mat-icon"),a(3,"visibility"),i()()()}}function ge(t,o){t&1&&f(0,"tr",33)}function Le(t,o){t&1&&f(0,"tr",34)}function ye(t,o){if(t&1&&(n(0,"div")(1,"table",16),p(2,17),m(3,ce,2,0,"th",18)(4,de,2,1,"td",19),v(),p(5,20),m(6,_e,2,0,"th",18)(7,se,2,1,"td",19),v(),p(8,36),m(9,pe,2,0,"th",18)(10,ve,2,1,"td",19),v(),p(11,37),m(12,fe,2,0,"th",18)(13,Ce,2,1,"td",19),v(),p(14,23),m(15,xe,2,0,"th",18)(16,ue,3,2,"td",19),v(),p(17,24),m(18,Se,2,0,"th",18)(19,he,3,2,"td",19),v(),p(20,26),m(21,De,2,0,"th",18)(22,Ee,4,0,"td",19),v(),m(23,ge,1,0,"tr",27)(24,Le,1,0,"tr",28),i()()),t&2){let e=_(2);l(),r("dataSource",e.costCenters),l(22),r("matHeaderRowDef",e.costCenterColumns),l(),r("matRowDefColumns",e.costCenterColumns)}}function Te(t,o){t&1&&(n(0,"div",35),a(1," No cost centers found for this location. "),i())}function Ie(t,o){if(t&1){let e=E();n(0,"div")(1,"mat-card")(2,"mat-card-header")(3,"div",5)(4,"mat-icon",6),a(5,"location_on"),i()(),n(6,"mat-card-title"),a(7),i(),m(8,It,2,1,"mat-card-subtitle",2),i(),n(9,"mat-card-content")(10,"div",7)(11,"h3"),a(12,"Location Details"),i(),n(13,"mat-list"),m(14,Mt,6,1,"mat-list-item",2)(15,wt,1,0,"mat-divider",2)(16,bt,5,1,"mat-list-item",2)(17,kt,1,0,"mat-divider",2)(18,Pt,5,1,"mat-list-item",2)(19,At,1,0,"mat-divider",2)(20,Vt,5,5,"mat-list-item",2)(21,Bt,1,0,"mat-divider",2)(22,$t,5,1,"mat-list-item",2)(23,Nt,1,0,"mat-divider",2)(24,Rt,5,1,"mat-list-item",2)(25,Ot,1,0,"mat-divider",2)(26,Ht,5,1,"mat-list-item",2)(27,jt,1,0,"mat-divider",2),n(28,"mat-list-item")(29,"span",8),a(30,"Status"),i(),n(31,"span",9),a(32),i()(),f(33,"mat-divider"),n(34,"mat-list-item")(35,"span",8),a(36,"Created At"),i(),n(37,"span",9),a(38),y(39,"date"),i()(),f(40,"mat-divider"),m(41,Ft,6,4,"mat-list-item",2),i()(),n(42,"div",10)(43,"h3"),a(44,"Stores"),i(),n(45,"div",11)(46,"button",12),S("click",function(){C(e);let s=_();return x(s.addStore())}),n(47,"mat-icon"),a(48,"add"),i(),a(49," Add Store "),i()(),m(50,re,28,3,"div",2)(51,me,2,0,"div",13),i(),n(52,"div",10)(53,"h3"),a(54,"Cost Centers"),i(),m(55,ye,25,3,"div",2)(56,Te,2,0,"div",13),i()(),n(57,"mat-card-actions")(58,"button",12),S("click",function(){C(e);let s=_();return x(s.editLocation())}),n(59,"mat-icon"),a(60,"edit"),i(),a(61," Edit "),i(),n(62,"button",14),S("click",function(){C(e);let s=_();return x(s.goBack())}),n(63,"mat-icon"),a(64,"arrow_back"),i(),a(65," Back "),i()()()()}if(t&2){let e=_();l(7),d(e.location.name),l(),r("ngIf",e.location.code),l(6),r("ngIf",e.location.companyName),l(),r("ngIf",e.location.companyName),l(),r("ngIf",e.location.description),l(),r("ngIf",e.location.description),l(),r("ngIf",e.location.address),l(),r("ngIf",e.location.address),l(),r("ngIf",e.location.city||e.location.state||e.location.country),l(),r("ngIf",e.location.city||e.location.state||e.location.country),l(),r("ngIf",e.location.postalCode),l(),r("ngIf",e.location.postalCode),l(),r("ngIf",e.location.phone),l(),r("ngIf",e.location.phone),l(),r("ngIf",e.location.email),l(),r("ngIf",e.location.email),l(5),d(e.location.isActive?"Active":"Inactive"),l(6),d(T(39,23,e.location.createdAt,"medium")),l(3),r("ngIf",e.location.updatedAt),l(9),r("ngIf",e.stores.length>0),l(),r("ngIf",e.stores.length===0),l(4),r("ngIf",e.costCenters.length>0),l(),r("ngIf",e.costCenters.length===0)}}var ii=(()=>{class t{constructor(e,c,s,D,Lt,yt){this.route=e,this.router=c,this.locationService=s,this.storeService=D,this.costCenterService=Lt,this.snackBar=yt,this.location=null,this.stores=[],this.costCenters=[],this.isLoading=!1,this.storeColumns=["name","code","city","country","isSalesPoint","isActive","costCentersCount","actions"],this.costCenterColumns=["name","code","storeName","typeName","isSalesPoint","isActive","actions"]}ngOnInit(){this.loadLocationData()}loadLocationData(){let e=this.route.snapshot.paramMap.get("id");if(!e){this.snackBar.open("Location ID is missing","Close",{duration:3e3}),this.router.navigate(["/locations"]);return}this.isLoading=!0,I({location:this.locationService.getById(+e).pipe(L(c=>(console.error("Error loading location:",c),this.snackBar.open("Failed to load location details","Close",{duration:3e3}),g(null)))),stores:this.storeService.getByLocation(+e).pipe(L(c=>(console.error("Error loading stores:",c),g([])))),costCenters:this.costCenterService.getByLocation(+e).pipe(L(c=>(console.error("Error loading cost centers:",c),g([]))))}).pipe(M(()=>this.isLoading=!1)).subscribe(c=>{this.location=c.location,this.stores=c.stores,this.costCenters=c.costCenters,this.location||this.router.navigate(["/locations"])})}goBack(){this.router.navigate(["/locations"])}editLocation(){this.location&&this.router.navigate(["/locations/edit",this.location.id])}viewStore(e){this.router.navigate(["/stores",e])}viewCostCenter(e){this.router.navigate(["/cost-centers",e])}addStore(){this.location&&this.router.navigate(["/stores/new"],{queryParams:{locationId:this.location.id}})}goToCompany(){this.location&&this.location.companyId&&this.router.navigate(["/companies",this.location.companyId])}static{this.\u0275fac=function(c){return new(c||t)(u(B),u($),u(Dt),u(Et),u(gt),u(xt))}}static{this.\u0275cmp=w({type:t,selectors:[["app-location-detail"]],standalone:!0,features:[k],decls:3,vars:2,consts:[[1,"container"],["class","loading-shade",4,"ngIf"],[4,"ngIf"],[1,"loading-shade"],["diameter","50"],["mat-card-avatar",""],["color","primary"],[1,"tab-content"],["matListItemTitle",""],["matListItemLine",""],[1,"tab-content","mt-4"],[1,"action-bar"],["mat-raised-button","","color","primary",3,"click"],["class","no-data-message",4,"ngIf"],["mat-button","",3,"click"],[1,"link",3,"click"],["mat-table","",1,"full-width",3,"dataSource"],["matColumnDef","name"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","code"],["matColumnDef","city"],["matColumnDef","country"],["matColumnDef","isSalesPoint"],["matColumnDef","isActive"],["matColumnDef","costCentersCount"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["mat-header-cell",""],["mat-cell",""],[3,"color"],["mat-icon-button","","color","primary","matTooltip","View",3,"click"],["mat-header-row",""],["mat-row",""],[1,"no-data-message"],["matColumnDef","storeName"],["matColumnDef","typeName"]],template:function(c,s){c&1&&(n(0,"div",0),m(1,Tt,2,0,"div",1)(2,Ie,66,26,"div",2),i()),c&2&&(l(),r("ngIf",s.isLoading),l(),r("ngIf",s.location))},dependencies:[V,P,A,N,H,R,O,nt,W,tt,it,Y,et,Z,X,F,j,ht,St,ut,q,z,Q,G,K,U,J,vt,ot,lt,dt,rt,at,_t,mt,ct,st,pt,Ct,ft],styles:[".container[_ngcontent-%COMP%]{padding:20px;position:relative;min-height:400px}.loading-shade[_ngcontent-%COMP%]{position:absolute;inset:0;background:#00000026;z-index:1;display:flex;align-items:center;justify-content:center}mat-card[_ngcontent-%COMP%]{max-width:1000px;margin:0 auto}mat-card-actions[_ngcontent-%COMP%]{padding:16px;display:flex;gap:10px}mat-list-item[_ngcontent-%COMP%]{height:auto!important;padding:16px 0}.tab-content[_ngcontent-%COMP%]{padding:20px 0}.action-bar[_ngcontent-%COMP%]{margin-bottom:20px;display:flex;justify-content:flex-end}.full-width[_ngcontent-%COMP%]{width:100%}.no-data-message[_ngcontent-%COMP%]{text-align:center;padding:20px;font-style:italic;color:#0000008a}.link[_ngcontent-%COMP%]{color:#3f51b5;cursor:pointer;text-decoration:underline}"]})}}return t})();export{ii as LocationDetailComponent};
