<div class="credit-note-detail-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <span *ngIf="!isEditMode && !isViewMode">New Credit Note</span>
        <span *ngIf="isEditMode">Edit Credit Note</span>
        <span *ngIf="isViewMode">Credit Note Details</span>
      </mat-card-title>
      <mat-card-subtitle *ngIf="creditNote">
        {{ creditNote.transactionNumber }} - {{ creditNote.status }}
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading credit note...</p>
      </div>

      <form [formGroup]="creditNoteForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
        <!-- Header Information -->
        <div class="form-section">
          <h3>Credit Note Information</h3>
          <div class="form-row">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Cost Center</mat-label>
              <mat-select formControlName="sourceCostCenterId" required (selectionChange)="onCostCenterChange()">
                <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
                  {{ costCenter.name }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="creditNoteForm.get('sourceCostCenterId')?.hasError('required')">
                Cost Center is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Supplier</mat-label>
              <mat-select formControlName="supplierId">
                <mat-option value="">None</mat-option>
                <mat-option *ngFor="let supplier of suppliers" [value]="supplier.id">
                  {{ supplier.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Related Transaction</mat-label>
              <mat-select formControlName="relatedTransactionId" (selectionChange)="onRelatedTransactionChange($event.value)">
                <mat-option value="">None</mat-option>
                <mat-option *ngFor="let transaction of receivingTransactions" [value]="transaction.id">
                  {{ transaction.referenceNumber }} - {{ transaction.supplierName }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Transaction Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="transactionDate" required>
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="creditNoteForm.get('transactionDate')?.hasError('required')">
                Transaction Date is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Reference Number</mat-label>
              <input matInput formControlName="referenceNumber" placeholder="Optional reference number">
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Reason</mat-label>
              <mat-select formControlName="reason" required>
                <mat-option *ngFor="let reason of creditNoteReasons" [value]="reason">
                  {{ reason }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="creditNoteForm.get('reason')?.hasError('required')">
                Reason is required
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Notes</mat-label>
              <textarea matInput formControlName="notes" rows="3" placeholder="Additional notes..."></textarea>
            </mat-form-field>
          </div>
        </div>

        <!-- Credit Note Details -->
        <div class="form-section">
          <div class="section-header">
            <h3>Credit Note Items</h3>
            <button type="button" mat-raised-button color="primary" (click)="addDetail()" [disabled]="isViewMode">
              <mat-icon>add</mat-icon>
              Add Item
            </button>
          </div>

          <div class="details-table-container" *ngIf="detailsArray.length > 0">
            <table mat-table [dataSource]="detailsArray.controls" class="details-table">
              <!-- Product Code Column -->
              <ng-container matColumnDef="productCode">
                <th mat-header-cell *matHeaderCellDef>Product Code</th>
                <td mat-cell *matCellDef="let detail; let i = index">
                  {{ getProductCode(detail.get('productId')?.value) }}
                </td>
              </ng-container>

              <!-- Product Name Column -->
              <ng-container matColumnDef="productName">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let detail; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <mat-select [formControl]="detail.get('productId')" required>
                      <mat-option *ngFor="let product of products" [value]="product.id">
                        {{ product.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Quantity</th>
                <td mat-cell *matCellDef="let detail; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="detail.get('quantity')" min="0.01" step="0.01" required>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Unit Price Column -->
              <ng-container matColumnDef="unitPrice">
                <th mat-header-cell *matHeaderCellDef>Unit Price</th>
                <td mat-cell *matCellDef="let detail; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <input matInput type="number" [formControl]="detail.get('unitPrice')" min="0" step="0.01" required>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Line Total Column -->
              <ng-container matColumnDef="lineTotal">
                <th mat-header-cell *matHeaderCellDef>Total</th>
                <td mat-cell *matCellDef="let detail; let i = index">
                  {{ calculateLineTotal(detail) | currency:'USD':'symbol':'1.2-2' }}
                </td>
              </ng-container>

              <!-- Reason Column -->
              <ng-container matColumnDef="reason">
                <th mat-header-cell *matHeaderCellDef>Reason</th>
                <td mat-cell *matCellDef="let detail; let i = index">
                  <mat-form-field appearance="outline" class="table-form-field">
                    <mat-select [formControl]="detail.get('reason')" required>
                      <mat-option *ngFor="let reason of creditNoteReasons" [value]="reason">
                        {{ reason }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let detail; let i = index">
                  <button type="button" mat-icon-button color="warn" (click)="removeDetail(i)" [disabled]="isViewMode">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>

          <div *ngIf="detailsArray.length === 0" class="no-details">
            <mat-icon>inventory_2</mat-icon>
            <p>No items added yet. Click "Add Item" to get started.</p>
          </div>

          <!-- Total Amount -->
          <div class="total-section" *ngIf="detailsArray.length > 0">
            <div class="total-row">
              <strong>Total Amount: {{ calculateTotalAmount() | currency:'USD':'symbol':'1.2-2' }}</strong>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" mat-button (click)="onCancel()">Cancel</button>
          <button type="submit" mat-raised-button color="primary" [disabled]="!creditNoteForm.valid || saving || isViewMode">
            <mat-spinner diameter="20" *ngIf="saving"></mat-spinner>
            <span *ngIf="!saving">{{ isEditMode ? 'Update' : 'Create' }} Credit Note</span>
            <span *ngIf="saving">{{ isEditMode ? 'Updating...' : 'Creating...' }}</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
