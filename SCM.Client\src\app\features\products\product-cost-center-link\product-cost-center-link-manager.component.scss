.page-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
  
  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: #333;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  p {
    margin-top: 16px;
    color: #666;
  }
}

.product-selection-card,
.product-details-card,
.cost-centers-card {
  margin-bottom: 20px;
}

.product-selection-card {
  .full-width {
    width: 100%;
    max-width: 500px;
  }
}

.product-details-card {
  .product-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    
    .info-row {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
      
      label {
        font-weight: 500;
        color: #333;
        min-width: 120px;
      }
      
      span {
        color: #666;
        font-family: monospace;
      }
    }
  }
}

.cost-centers-card {
  .search-container {
    margin-bottom: 20px;
    
    .search-field {
      width: 100%;
      max-width: 400px;
    }
  }
  
  .table-container {
    overflow-x: auto;
    
    .cost-centers-table {
      width: 100%;
      min-width: 800px;
      
      .mat-mdc-header-cell {
        font-weight: 600;
        color: #333;
        background-color: #f8f9fa;
      }
      
      .mat-mdc-cell {
        padding: 8px 16px;
      }
      
      .checkbox-column {
        width: 80px;
        text-align: center;
      }
      
      .stock-input {
        width: 100px;
        
        .mat-mdc-form-field-wrapper {
          padding-bottom: 0;
        }
        
        .mat-mdc-text-field-wrapper {
          height: 40px;
        }
        
        input {
          text-align: center;
        }
      }
      
      .stock-value {
        font-family: monospace;
        font-weight: 500;
        color: #2e7d32;
      }
      
      .disabled-field {
        color: #999;
        font-style: italic;
      }
      
      .linked-row {
        background-color: #f0f8ff;
        
        &:hover {
          background-color: #e6f3ff;
        }
      }
      
      .mat-mdc-row:hover {
        background-color: #f5f5f5;
      }
    }
  }
  
  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
    
    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
    
    p {
      margin: 0;
      font-size: 16px;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

// Responsive design
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .product-details-card .product-info {
    grid-template-columns: 1fr;
  }
  
  .cost-centers-card .table-container {
    .cost-centers-table {
      min-width: 600px;
    }
    
    .stock-input {
      width: 80px;
    }
  }
}

// Material theme overrides
.mat-mdc-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.mat-mdc-card-header {
  padding-bottom: 8px;
}

.mat-mdc-card-title {
  font-size: 18px;
  font-weight: 500;
}

.mat-mdc-card-subtitle {
  color: #666;
  font-size: 14px;
}

.mat-mdc-table {
  border-radius: 8px;
  overflow: hidden;
}

.mat-mdc-header-row {
  background-color: #f8f9fa;
}

.mat-mdc-checkbox {
  --mdc-checkbox-selected-icon-color: #1976d2;
  --mdc-checkbox-selected-hover-icon-color: #1565c0;
  --mdc-checkbox-selected-pressed-icon-color: #0d47a1;
}
