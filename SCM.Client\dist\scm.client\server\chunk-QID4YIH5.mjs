import './polyfills.server.mjs';
import{a as o}from"./chunk-I44XXN6Y.mjs";import{a as h}from"./chunk-A3ICGYXF.mjs";import{Rc as a,Sc as s,Z as p,ca as i}from"./chunk-FFIKRRNO.mjs";var d=(()=>{class r{constructor(t,e){this.apiService=t,this.http=e,this.path="products",this.apiUrl=h.apiUrl}getAll(){return this.apiService.get(this.path)}getAllProducts(){return this.getAll()}getById(t){return this.apiService.get(`${this.path}/${t}`)}getByCode(t){return this.apiService.get(`${this.path}/code/${t}`)}getByDepartmentId(t){return this.apiService.get(`${this.path}/department/${t}`)}getByGroupId(t){return this.apiService.get(`${this.path}/group/${t}`)}getBySubGroupId(t){return this.apiService.get(`${this.path}/subgroup/${t}`)}search(t){let e=new a().set("term",t);return this.apiService.get(`${this.path}/search`,e)}create(t){return this.apiService.post(this.path,t)}update(t,e){return this.apiService.put(`${this.path}/${t}`,e)}delete(t){return this.apiService.delete(`${this.path}/${t}`)}importProducts(t){let e=new FormData;return e.append("file",t),this.http.post(`${this.apiUrl}/${this.path}/import`,e)}downloadImportTemplate(){return this.http.get(`${this.apiUrl}/${this.path}/import/template`,{responseType:"blob"})}static{this.\u0275fac=function(e){return new(e||r)(i(o),i(s))}}static{this.\u0275prov=p({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();export{d as a};
