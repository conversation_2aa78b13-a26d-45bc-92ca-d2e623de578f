import './polyfills.server.mjs';
import{a as zt,b as Vt,c as Qt}from"./chunk-GMANBD54.mjs";import{a as wt,b as Dt,c as Ft,d as Pt}from"./chunk-7XHZDQHT.mjs";import{a as yt,b as bt}from"./chunk-J2FNDWGV.mjs";import{b as Et,c as Mt}from"./chunk-GBVBP5JO.mjs";import{a as gt,b as Tt,e as xt,h as kt,i as vt}from"./chunk-KCBYLGU4.mjs";import{b as G,d as S,f as H,g as j,h as L,k as J,l as W,m as K,n as U,q as X,u as Y,w as Z}from"./chunk-R2YOBFRV.mjs";import{a as lt,b as st,c as ct,d as dt,e as pt,f as ft,g as ut,h as Ct,i as _t,j as St,l as ht}from"./chunk-CUT3Q574.mjs";import{a as It,b as Ot}from"./chunk-4QI52JLP.mjs";import{h as mt}from"./chunk-PWFZPCXU.mjs";import{a as Nt,b as Bt,f as Rt}from"./chunk-NZ6MYAVT.mjs";import{K as tt,U as et,_ as nt,aa as rt,ba as it,ca as ot,da as at}from"./chunk-N6IFEGQ2.mjs";import{Eb as E,Ec as z,Fb as o,Fc as V,Gb as _,Gc as Q,Hb as y,Ic as $,Jb as I,Jc as A,Ka as m,La as b,Lc as q,Qb as O,S as F,Tb as N,Wb as M,Xb as B,Yb as R,ab as d,cb as s,ha as P,lb as i,mb as n,nb as p,oa as h,ob as f,p as D,pa as g,pb as u,rb as k,ub as T,wb as v}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";var $t=(t,r)=>({"status-completed":t,"status-pending":r});function At(t,r){if(t&1&&(i(0,"mat-option",46),o(1),n()),t&2){let e=r.$implicit;s("value",e.id),m(),y(" ",e.name," ")}}function qt(t,r){t&1&&(i(0,"mat-error"),o(1," From Cost Center is required "),n())}function Gt(t,r){if(t&1&&(i(0,"mat-option",46),o(1),n()),t&2){let e=r.$implicit;s("value",e.id),m(),y(" ",e.name," ")}}function Ht(t,r){t&1&&(i(0,"mat-error"),o(1," To Cost Center is required "),n())}function jt(t,r){t&1&&(i(0,"mat-error"),o(1," From and To Cost Centers cannot be the same "),n())}function Lt(t,r){t&1&&(i(0,"th",47),o(1,"Product"),n())}function Jt(t,r){if(t&1&&(i(0,"mat-option",46),o(1),n()),t&2){let e=r.$implicit;s("value",e.id),m(),I(" ",e.id," - ",e.name," (",e.unitSize,") ")}}function Wt(t,r){t&1&&(i(0,"mat-error"),o(1," Product is required "),n())}function Kt(t,r){if(t&1&&(i(0,"td",48)(1,"mat-form-field",49),p(2,"input",50),i(3,"mat-autocomplete",51,1),d(5,Jt,2,4,"mat-option",18),M(6,"async"),n(),d(7,Wt,2,0,"mat-error",19),n()()),t&2){let e,l=r.$implicit,a=r.index,c=E(4),C=v();m(2),s("formControl",l.get("productId"))("matAutocomplete",c),m(),s("displayWith",C.displayProductFn),m(2),s("ngForOf",B(6,5,C.filteredProducts[a])),m(2),s("ngIf",(e=l.get("productId"))==null?null:e.hasError("required"))}}function Ut(t,r){t&1&&(i(0,"th",47),o(1,"Description"),n())}function Xt(t,r){if(t&1&&(i(0,"td",48)(1,"mat-form-field",49),p(2,"input",52),n()()),t&2){let e=r.$implicit;m(2),s("formControl",e.get("productName"))}}function Yt(t,r){t&1&&(i(0,"th",47),o(1,"Unit Size"),n())}function Zt(t,r){if(t&1&&(i(0,"td",48)(1,"mat-form-field",49),p(2,"input",52),n()()),t&2){let e=r.$implicit;m(2),s("formControl",e.get("unitSize"))}}function te(t,r){t&1&&(i(0,"th",47),o(1,"Available Stock"),n())}function ee(t,r){if(t&1&&(i(0,"td",48)(1,"mat-form-field",49),p(2,"input",53),n()()),t&2){let e=r.$implicit;m(2),s("formControl",e.get("availableStock"))}}function ne(t,r){t&1&&(i(0,"th",47),o(1,"Transfer Qty"),n())}function re(t,r){t&1&&(i(0,"mat-error"),o(1," Transfer quantity is required "),n())}function ie(t,r){t&1&&(i(0,"mat-error"),o(1," Must be greater than 0 "),n())}function oe(t,r){t&1&&(i(0,"mat-error"),o(1," Exceeds available stock "),n())}function ae(t,r){if(t&1&&(i(0,"td",48)(1,"mat-form-field",49),p(2,"input",54),d(3,re,2,0,"mat-error",19)(4,ie,2,0,"mat-error",19)(5,oe,2,0,"mat-error",19),n()()),t&2){let e,l,a,c=r.$implicit;m(2),s("formControl",c.get("transferQty")),m(),s("ngIf",(e=c.get("transferQty"))==null?null:e.hasError("required")),m(),s("ngIf",(l=c.get("transferQty"))==null?null:l.hasError("min")),m(),s("ngIf",(a=c.get("transferQty"))==null?null:a.hasError("exceedsStock"))}}function me(t,r){t&1&&p(0,"th",47)}function le(t,r){if(t&1){let e=k();i(0,"td",48)(1,"button",55),T("click",function(){let a=h(e).index,c=v();return g(c.removeItem(a))}),i(2,"mat-icon"),o(3,"delete"),n()()()}}function se(t,r){t&1&&p(0,"tr",56)}function ce(t,r){t&1&&p(0,"tr",57)}function de(t,r){t&1&&(i(0,"th",47),o(1,"Transfer ID"),n())}function pe(t,r){if(t&1&&(i(0,"td",48),o(1),n()),t&2){let e=r.$implicit;m(),_(e.id)}}function fe(t,r){t&1&&(i(0,"th",47),o(1,"Date"),n())}function ue(t,r){if(t&1&&(i(0,"td",48),o(1),M(2,"date"),n()),t&2){let e=r.$implicit;m(),_(R(2,1,e.date,"shortDate"))}}function Ce(t,r){t&1&&(i(0,"th",47),o(1,"From"),n())}function _e(t,r){if(t&1&&(i(0,"td",48),o(1),n()),t&2){let e=r.$implicit;m(),_(e.fromCostCenter)}}function Se(t,r){t&1&&(i(0,"th",47),o(1,"To"),n())}function he(t,r){if(t&1&&(i(0,"td",48),o(1),n()),t&2){let e=r.$implicit;m(),_(e.toCostCenter)}}function ge(t,r){t&1&&(i(0,"th",47),o(1,"Status"),n())}function Te(t,r){if(t&1&&(i(0,"td",58),o(1),n()),t&2){let e=r.$implicit;s("ngClass",N(2,$t,e.status==="Completed",e.status==="Pending")),m(),y(" ",e.status," ")}}function xe(t,r){t&1&&(i(0,"th",47),o(1,"Created By"),n())}function ke(t,r){if(t&1&&(i(0,"td",48),o(1),n()),t&2){let e=r.$implicit;m(),_(e.createdBy)}}function ve(t,r){t&1&&(i(0,"th",47),o(1,"Actions"),n())}function ye(t,r){if(t&1){let e=k();i(0,"td",48)(1,"button",59),T("click",function(){let a=h(e).$implicit,c=v();return g(c.viewTransfer(a))}),i(2,"mat-icon"),o(3,"visibility"),n()()()}}function be(t,r){t&1&&p(0,"tr",56)}function Ee(t,r){t&1&&p(0,"tr",57)}var nn=(()=>{class t{constructor(e,l){this.fb=e,this.snackBar=l,this.transferNo="TRF-00001",this.currentDate=new Date,this.displayedColumns=["productId","productName","unitSize","availableStock","transferQty","actions"],this.historyColumns=["id","date","fromCostCenter","toCostCenter","status","createdBy","actions"],this.products=[{id:"P001",name:"Rice",unitSize:"25kg",currentStock:120},{id:"P002",name:"Flour",unitSize:"10kg",currentStock:85},{id:"P003",name:"Soft Drinks",unitSize:"24x330ml",currentStock:45},{id:"P004",name:"Cleaning Liquid",unitSize:"5L",currentStock:32},{id:"P005",name:"Light Bulbs",unitSize:"10pcs",currentStock:15},{id:"P006",name:"Cigarettes",unitSize:"200pcs",currentStock:25},{id:"P007",name:"Paper Towels",unitSize:"12 rolls",currentStock:40},{id:"P008",name:"Coffee",unitSize:"1kg",currentStock:18}],this.transfers=[{id:"TRF-00001",date:new Date(2025,4,5),fromCostCenter:"1 - Food Store",toCostCenter:"7 - Food Main Kitchen",status:"Completed",createdBy:"John Doe"},{id:"TRF-00002",date:new Date(2025,4,3),fromCostCenter:"2 - Beverage Store",toCostCenter:"8 - Main Restaurant",status:"Completed",createdBy:"John Doe"},{id:"TRF-00003",date:new Date(2025,4,1),fromCostCenter:"3 - General Store",toCostCenter:"4 - Engineering Store",status:"Pending",createdBy:"Jane Smith"},{id:"TRF-00004",date:new Date(2025,3,28),fromCostCenter:"1 - Food Store",toCostCenter:"8 - Main Restaurant",status:"Completed",createdBy:"John Doe"}],this.filteredProducts=[],this.costCenters=[{id:1,name:"1 - Food Store"},{id:2,name:"2 - Beverage Store"},{id:3,name:"3 - General Store"},{id:4,name:"4 - Engineering Store"},{id:5,name:"5 - S.O.E. Store"},{id:6,name:"6 - Tobacco"},{id:7,name:"7 - Food Main Kitchen"},{id:8,name:"8 - Main Restaurant"}]}ngOnInit(){this.initForm()}initForm(){this.transferForm=this.fb.group({transferDate:[this.currentDate,S.required],fromCostCenter:["",S.required],toCostCenter:["",S.required],notes:[""],items:this.fb.array([])}),this.addItem(),this.transferForm.get("fromCostCenter")?.valueChanges.subscribe(()=>{this.validateCostCenters()}),this.transferForm.get("toCostCenter")?.valueChanges.subscribe(()=>{this.validateCostCenters()})}validateCostCenters(){let e=this.transferForm.get("fromCostCenter")?.value,l=this.transferForm.get("toCostCenter")?.value;if(e&&l&&e===l)this.transferForm.get("toCostCenter")?.setErrors({sameCostCenter:!0});else{let a=this.transferForm.get("toCostCenter")?.errors;a&&(delete a.sameCostCenter,Object.keys(a).length===0?this.transferForm.get("toCostCenter")?.setErrors(null):this.transferForm.get("toCostCenter")?.setErrors(a))}}get items(){return this.transferForm.get("items")}addItem(){let e=this.fb.group({productId:["",S.required],productName:[""],unitSize:[""],availableStock:[{value:0,disabled:!0}],transferQty:[0,[S.required,S.min(1)]]}),l=this.items.length;this.setupProductAutocomplete(e,l),e.get("transferQty")?.valueChanges.subscribe(()=>{this.validateTransferQty(e)}),this.items.push(e)}setupProductAutocomplete(e,l){let a=e.get("productId");this.filteredProducts[l]=a.valueChanges.pipe(F(""),D(c=>this._filterProducts(c||""))),a?.valueChanges.subscribe(c=>{let C=this.products.find(x=>x.id===c);C?(e.patchValue({productName:C.name,unitSize:C.unitSize,availableStock:C.currentStock}),this.validateTransferQty(e)):e.patchValue({productName:"",unitSize:"",availableStock:0})})}_filterProducts(e){let l=e.toLowerCase();return this.products.filter(a=>a.id.toLowerCase().includes(l)||a.name.toLowerCase().includes(l))}validateTransferQty(e){let l=e.get("availableStock")?.value||0;if((e.get("transferQty")?.value||0)>l)e.get("transferQty")?.setErrors({exceedsStock:!0});else{let c=e.get("transferQty")?.errors;c&&(delete c.exceedsStock,Object.keys(c).length===0?e.get("transferQty")?.setErrors(null):e.get("transferQty")?.setErrors(c))}}removeItem(e){this.items.removeAt(e),this.filteredProducts.splice(e,1)}displayProductFn(e){if(!e)return"";let l=this.products.find(a=>a.id===e);return l?`${l.id} - ${l.name}`:""}saveTransfer(){this.transferForm.valid?(console.log("Transfer data:",this.transferForm.getRawValue()),this.snackBar.open("Stock transfer saved successfully","Close",{duration:3e3}),this.initForm()):(this.markFormGroupTouched(this.transferForm),this.snackBar.open("Please fix the errors in the form","Close",{duration:3e3}))}viewTransfer(e){console.log("View transfer:",e)}markFormGroupTouched(e){Object.values(e.controls).forEach(l=>{l.markAsTouched(),l instanceof L&&this.markFormGroupTouched(l)})}static{this.\u0275fac=function(l){return new(l||t)(b(Y),b(It))}}static{this.\u0275cmp=P({type:t,selectors:[["app-stock-transfer"]],standalone:!0,features:[O],decls:104,vars:15,consts:[["picker",""],["auto","matAutocomplete"],[1,"page-container"],[1,"page-header"],["label","New Transfer"],[1,"tab-content"],[1,"page-subheader"],[1,"header-actions"],["mat-raised-button","","color","primary",3,"click"],[3,"formGroup"],[1,"form-row"],["appearance","outline"],["matInput","","formControlName","transferDate",3,"matDatepicker"],["matSuffix","",3,"for"],[1,"spacer"],[1,"reference-number"],[1,"reference-value"],["formControlName","fromCostCenter"],[3,"value",4,"ngFor","ngForOf"],[4,"ngIf"],["formControlName","toCostCenter"],["appearance","outline",1,"full-width"],["matInput","","formControlName","notes","rows","2"],[1,"table-container","mat-elevation-z2"],["mat-table","",3,"dataSource"],["matColumnDef","productId"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","productName"],["matColumnDef","unitSize"],["matColumnDef","availableStock"],["matColumnDef","transferQty"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],[1,"table-actions"],["mat-button","","color","primary",3,"click"],["label","Transfer History"],["mat-table","",1,"history-table",3,"dataSource"],["matColumnDef","id"],["matColumnDef","date"],["matColumnDef","fromCostCenter"],["matColumnDef","toCostCenter"],["matColumnDef","status"],["mat-cell","",3,"ngClass",4,"matCellDef"],["matColumnDef","createdBy"],[3,"value"],["mat-header-cell",""],["mat-cell",""],["appearance","outline",1,"table-form-field"],["matInput","",3,"formControl","matAutocomplete"],[3,"displayWith"],["matInput","","readonly","",3,"formControl"],["matInput","","type","number","readonly","",3,"formControl"],["matInput","","type","number",3,"formControl"],["mat-icon-button","","color","warn",3,"click"],["mat-header-row",""],["mat-row",""],["mat-cell","",3,"ngClass"],["mat-icon-button","","color","primary",3,"click"]],template:function(l,a){if(l&1){let c=k();i(0,"div",2)(1,"div",3)(2,"h1"),o(3,"Stock Transfer"),n()(),i(4,"mat-tab-group")(5,"mat-tab",4)(6,"div",5)(7,"div",6)(8,"h2"),o(9,"New Stock Transfer"),n(),i(10,"div",7)(11,"button",8),T("click",function(){return h(c),g(a.saveTransfer())}),o(12,"Save Transfer"),n()()(),i(13,"form",9)(14,"div",10)(15,"mat-form-field",11)(16,"mat-label"),o(17,"Date"),n(),p(18,"input",12)(19,"mat-datepicker-toggle",13)(20,"mat-datepicker",null,0),n(),p(22,"div",14),i(23,"div",15)(24,"span"),o(25,"Transfer No."),n(),i(26,"span",16),o(27),n()()(),i(28,"div",10)(29,"mat-form-field",11)(30,"mat-label"),o(31,"From Cost Center"),n(),i(32,"mat-select",17),d(33,At,2,2,"mat-option",18),n(),d(34,qt,2,0,"mat-error",19),n(),i(35,"mat-form-field",11)(36,"mat-label"),o(37,"To Cost Center"),n(),i(38,"mat-select",20),d(39,Gt,2,2,"mat-option",18),n(),d(40,Ht,2,0,"mat-error",19)(41,jt,2,0,"mat-error",19),n()(),i(42,"div",10)(43,"mat-form-field",21)(44,"mat-label"),o(45,"Notes"),n(),p(46,"textarea",22),n()(),i(47,"div",23)(48,"table",24),f(49,25),d(50,Lt,2,0,"th",26)(51,Kt,8,7,"td",27),u(),f(52,28),d(53,Ut,2,0,"th",26)(54,Xt,3,1,"td",27),u(),f(55,29),d(56,Yt,2,0,"th",26)(57,Zt,3,1,"td",27),u(),f(58,30),d(59,te,2,0,"th",26)(60,ee,3,1,"td",27),u(),f(61,31),d(62,ne,2,0,"th",26)(63,ae,6,4,"td",27),u(),f(64,32),d(65,me,1,0,"th",26)(66,le,4,0,"td",27),u(),d(67,se,1,0,"tr",33)(68,ce,1,0,"tr",34),n(),i(69,"div",35)(70,"button",36),T("click",function(){return h(c),g(a.addItem())}),i(71,"mat-icon"),o(72,"add"),n(),o(73," Add Item "),n()()()()()(),i(74,"mat-tab",37)(75,"div",5)(76,"div",6)(77,"h2"),o(78,"Transfer History"),n()(),i(79,"div",23)(80,"table",38),f(81,39),d(82,de,2,0,"th",26)(83,pe,2,1,"td",27),u(),f(84,40),d(85,fe,2,0,"th",26)(86,ue,3,4,"td",27),u(),f(87,41),d(88,Ce,2,0,"th",26)(89,_e,2,1,"td",27),u(),f(90,42),d(91,Se,2,0,"th",26)(92,he,2,1,"td",27),u(),f(93,43),d(94,ge,2,0,"th",26)(95,Te,2,5,"td",44),u(),f(96,45),d(97,xe,2,0,"th",26)(98,ke,2,1,"td",27),u(),f(99,32),d(100,ve,2,0,"th",26)(101,ye,4,0,"td",27),u(),d(102,be,1,0,"tr",33)(103,Ee,1,0,"tr",34),n()()()()()()}if(l&2){let c,C,x,w=E(21);m(13),s("formGroup",a.transferForm),m(5),s("matDatepicker",w),m(),s("for",w),m(8),_(a.transferNo),m(6),s("ngForOf",a.costCenters),m(),s("ngIf",(c=a.transferForm.get("fromCostCenter"))==null?null:c.hasError("required")),m(5),s("ngForOf",a.costCenters),m(),s("ngIf",(C=a.transferForm.get("toCostCenter"))==null?null:C.hasError("required")),m(),s("ngIf",(x=a.transferForm.get("toCostCenter"))==null?null:x.hasError("sameCostCenter")),m(7),s("dataSource",a.items.controls),m(19),s("matHeaderRowDef",a.displayedColumns),m(),s("matRowDefColumns",a.displayedColumns),m(12),s("dataSource",a.transfers),m(22),s("matHeaderRowDef",a.historyColumns),m(),s("matRowDefColumns",a.historyColumns)}},dependencies:[q,z,V,Q,$,A,Z,J,G,W,H,j,K,U,X,it,nt,rt,mt,Pt,wt,Dt,Ft,vt,kt,gt,Tt,xt,at,ot,Mt,Et,bt,yt,et,ht,lt,ct,ut,dt,st,Ct,pt,ft,_t,St,tt,Ot,Qt,zt,Vt,Rt,Nt,Bt],styles:[".page-container[_ngcontent-%COMP%]{padding:20px}.page-header[_ngcontent-%COMP%]{margin-bottom:20px}.tab-content[_ngcontent-%COMP%]{padding:20px 0}.page-subheader[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.header-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.form-row[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:16px;align-items:center}.full-width[_ngcontent-%COMP%]{width:100%}.spacer[_ngcontent-%COMP%]{flex:1}.reference-number[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end}.reference-value[_ngcontent-%COMP%]{font-weight:700;font-size:1.2em;color:#1976d2}.table-container[_ngcontent-%COMP%]{margin:20px 0;overflow-x:auto;border-radius:4px}table[_ngcontent-%COMP%]{width:100%}.table-form-field[_ngcontent-%COMP%]{width:100%;margin:0;font-size:14px}.table-form-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix{padding:8px 0;width:auto}.table-form-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper{padding:0 8px}.table-actions[_ngcontent-%COMP%]{padding:8px;display:flex;justify-content:flex-start}.history-table[_ngcontent-%COMP%]{width:100%}.status-completed[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.status-pending[_ngcontent-%COMP%]{color:#ff9800;font-weight:500}@media (max-width: 768px){.page-subheader[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.header-actions[_ngcontent-%COMP%]{margin-top:10px}.form-row[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.reference-number[_ngcontent-%COMP%]{align-items:flex-start;margin-top:10px}}"]})}}return t})();export{nn as StockTransferComponent};
