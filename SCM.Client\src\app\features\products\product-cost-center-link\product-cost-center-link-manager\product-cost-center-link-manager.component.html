<div class="page-container">
  <div class="page-header">
    <div class="header-content">
      <button mat-icon-button (click)="onCancel()" matTooltip="Back to list">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1>Product Cost Center Link</h1>
    </div>
  </div>

  <div *ngIf="isLoading && !selectedProduct" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading products...</p>
  </div>

  <!-- Product Selection -->
  <mat-card class="product-selection-card" *ngIf="!isLoading || selectedProduct">
    <mat-card-header>
      <mat-card-title>Select Product</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="productForm">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Search and Select Product</mat-label>
          <input matInput
                 formControlName="productSearch"
                 placeholder="Type to search products..."
                 [matAutocomplete]="productAuto">
          <mat-autocomplete #productAuto="matAutocomplete"
                           [displayWith]="displayProduct"
                           (optionSelected)="onProductSelected($event.option.value)">
            <mat-option *ngFor="let product of filteredProducts | async" [value]="product">
              {{product.code}} - {{product.name}}
            </mat-option>
          </mat-autocomplete>
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Product Details -->
  <mat-card class="product-details-card" *ngIf="selectedProduct">
    <mat-card-header>
      <mat-card-title>Product Details</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="product-info">
        <div class="info-row">
          <label>Product Code:</label>
          <span>{{selectedProduct.code}}</span>
        </div>
        <div class="info-row">
          <label>Product Name:</label>
          <span>{{selectedProduct.name}}</span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Cost Centers Table -->
  <mat-card class="cost-centers-card" *ngIf="selectedProduct">
    <mat-card-header>
      <mat-card-title>Cost Centers</mat-card-title>
      <mat-card-subtitle>Link product to cost centers and configure stock levels</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <!-- Search Filter -->
      <div class="search-container">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Filter Cost Centers</mat-label>
          <input matInput
                 [(ngModel)]="searchTerm"
                 (input)="onSearchChange(searchTerm)"
                 placeholder="Search by cost center name">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="30"></mat-spinner>
        <p>Loading cost centers...</p>
      </div>

      <!-- Cost Centers Table -->
      <div class="table-container" *ngIf="!isLoading">
        <table mat-table [dataSource]="filteredCostCenters" class="cost-centers-table">
          
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef class="checkbox-column">Choose</th>
            <td mat-cell *matCellDef="let costCenter" class="checkbox-column">
              <mat-checkbox 
                [checked]="costCenter.isLinked"
                [disabled]="isSaving"
                (change)="onLinkToggle(costCenter, $event.checked)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- Cost Center Name Column -->
          <ng-container matColumnDef="costCenterName">
            <th mat-header-cell *matHeaderCellDef>Cost Center Name</th>
            <td mat-cell *matCellDef="let costCenter">{{costCenter.costCenterName}}</td>
          </ng-container>

          <!-- Stock On Hand Column -->
          <ng-container matColumnDef="stockOnHand">
            <th mat-header-cell *matHeaderCellDef>Stock On Hand</th>
            <td mat-cell *matCellDef="let costCenter">
              <span class="stock-value">{{costCenter.stockOnHand | number:'1.2-2'}}</span>
            </td>
          </ng-container>

          <!-- Min Stock Column -->
          <ng-container matColumnDef="minStock">
            <th mat-header-cell *matHeaderCellDef>MinMum</th>
            <td mat-cell *matCellDef="let costCenter">
              <mat-form-field appearance="outline" class="stock-input" *ngIf="costCenter.isLinked">
                <input matInput
                       type="number"
                       [value]="costCenter.minStock || ''"
                       [disabled]="isSaving"
                       (blur)="onMinStockChange(costCenter, $event)"
                       placeholder="0">
              </mat-form-field>
              <span *ngIf="!costCenter.isLinked" class="disabled-field">-</span>
            </td>
          </ng-container>

          <!-- Max Stock Column -->
          <ng-container matColumnDef="maxStock">
            <th mat-header-cell *matHeaderCellDef>MaxiMum</th>
            <td mat-cell *matCellDef="let costCenter">
              <mat-form-field appearance="outline" class="stock-input" *ngIf="costCenter.isLinked">
                <input matInput
                       type="number"
                       [value]="costCenter.maxStock || ''"
                       [disabled]="isSaving"
                       (blur)="onMaxStockChange(costCenter, $event)"
                       placeholder="0">
              </mat-form-field>
              <span *ngIf="!costCenter.isLinked" class="disabled-field">-</span>
            </td>
          </ng-container>

          <!-- Reorder Point Column -->
          <ng-container matColumnDef="reorderPoint">
            <th mat-header-cell *matHeaderCellDef>ReOrder</th>
            <td mat-cell *matCellDef="let costCenter">
              <mat-form-field appearance="outline" class="stock-input" *ngIf="costCenter.isLinked">
                <input matInput
                       type="number"
                       [value]="costCenter.reorderPoint || ''"
                       [disabled]="isSaving"
                       (blur)="onReorderPointChange(costCenter, $event)"
                       placeholder="0">
              </mat-form-field>
              <span *ngIf="!costCenter.isLinked" class="disabled-field">-</span>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
              [class.linked-row]="row.isLinked"></tr>
        </table>

        <div *ngIf="filteredCostCenters.length === 0" class="no-data">
          <mat-icon>info</mat-icon>
          <p>No cost centers found</p>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Action Buttons -->
  <div class="action-buttons" *ngIf="selectedProduct">
    <button mat-raised-button (click)="onCancel()">
      <mat-icon>close</mat-icon>
      Close
    </button>
  </div>
</div>
