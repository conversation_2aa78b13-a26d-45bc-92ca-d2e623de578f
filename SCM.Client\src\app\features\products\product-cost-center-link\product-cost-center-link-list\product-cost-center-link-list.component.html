<div class="page-container">
  <div class="page-header">
    <h1>Product Cost Center Links</h1>
    <div class="header-actions">
      <button mat-raised-button color="accent" (click)="openManager()">
        <mat-icon>link</mat-icon> Link Manager
      </button>
      <button mat-raised-button color="primary" (click)="addLink()">
        <mat-icon>add</mat-icon> Add Link
      </button>
    </div>
  </div>

  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-container">
        <mat-form-field appearance="outline">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by product or cost center">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Product</mat-label>
          <mat-select [(ngModel)]="selectedProductId" (selectionChange)="applyFilter()">
            <mat-option [value]="null">All Products</mat-option>
            <mat-option *ngFor="let product of products" [value]="product.id">
              {{product.code}} - {{product.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Cost Center</mat-label>
          <mat-select [(ngModel)]="selectedCostCenterId" (selectionChange)="applyFilter()">
            <mat-option [value]="null">All Cost Centers</mat-option>
            <mat-option *ngFor="let costCenter of costCenters" [value]="costCenter.id">
              {{costCenter.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button color="primary" (click)="resetFilters()">
          <mat-icon>clear</mat-icon> Reset
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div class="table-container mat-elevation-z2">
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading product cost center links...</p>
    </div>

    <div *ngIf="!isLoading && filteredLinks.length === 0" class="empty-state">
      <mat-icon>link</mat-icon>
      <p>No product cost center links found</p>
      <button mat-raised-button color="primary" (click)="addLink()">Add Link</button>
    </div>

    <table mat-table [dataSource]="filteredLinks" matSort *ngIf="!isLoading && filteredLinks.length > 0">
      <!-- Product Code Column -->
      <ng-container matColumnDef="productCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Product Code</th>
        <td mat-cell *matCellDef="let link">{{link.productCode}}</td>
      </ng-container>

      <!-- Product Name Column -->
      <ng-container matColumnDef="productName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Product Name</th>
        <td mat-cell *matCellDef="let link">{{link.productName}}</td>
      </ng-container>

      <!-- Cost Center Name Column -->
      <ng-container matColumnDef="costCenterName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Cost Center</th>
        <td mat-cell *matCellDef="let link">{{link.costCenterName}}</td>
      </ng-container>

      <!-- Min Stock Column -->
      <ng-container matColumnDef="minStock">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Min Stock</th>
        <td mat-cell *matCellDef="let link">{{formatNumber(link.minStock)}}</td>
      </ng-container>

      <!-- Max Stock Column -->
      <ng-container matColumnDef="maxStock">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Max Stock</th>
        <td mat-cell *matCellDef="let link">{{formatNumber(link.maxStock)}}</td>
      </ng-container>

      <!-- Reorder Point Column -->
      <ng-container matColumnDef="reorderPoint">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Reorder Point</th>
        <td mat-cell *matCellDef="let link">{{formatNumber(link.reorderPoint)}}</td>
      </ng-container>

      <!-- Auto Reorder Column -->
      <ng-container matColumnDef="autoReorder">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Auto Reorder</th>
        <td mat-cell *matCellDef="let link">
          <mat-icon [color]="link.autoReorder ? 'primary' : 'warn'">
            {{link.autoReorder ? 'check_circle' : 'cancel'}}
          </mat-icon>
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let link">
          <button mat-icon-button [matMenuTriggerFor]="menu" matTooltip="More actions">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="editLink(link.id)">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </button>
            <button mat-menu-item (click)="deleteLink(link.id)" class="delete-action">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
</div>
