import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Router, RouterModule } from '@angular/router';
import { ProductService } from '../../../core/services/product.service';
import { ProductCostCenterLinkService } from '../../../core/services/product-cost-center-link.service';
import { Product } from '../../../core/models/product.model';
import { CostCenterWithLink, ToggleLinkRequest } from '../../../core/models/product-cost-center-link.model';
import { debounceTime, distinctUntilChanged, switchMap, startWith } from 'rxjs/operators';
import { Subject, combineLatest } from 'rxjs';

@Component({
  selector: 'app-product-cost-center-link-manager',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTableModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
    RouterModule
  ],
  templateUrl: './product-cost-center-link-manager.component.html',
  styleUrls: ['./product-cost-center-link-manager.component.scss']
})
export class ProductCostCenterLinkManagerComponent implements OnInit {
  displayedColumns: string[] = ['select', 'costCenterName', 'stockOnHand', 'minStock', 'maxStock', 'reorderPoint'];
  
  products: Product[] = [];
  costCenters: CostCenterWithLink[] = [];
  filteredCostCenters: CostCenterWithLink[] = [];
  
  selectedProduct: Product | null = null;
  searchTerm: string = '';
  isLoading: boolean = false;
  isSaving: boolean = false;
  
  searchSubject = new Subject<string>();
  
  productForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private productService: ProductService,
    private productCostCenterLinkService: ProductCostCenterLinkService,
    private snackBar: MatSnackBar,
    private router: Router,
    private dialog: MatDialog
  ) {
    this.productForm = this.fb.group({
      selectedProductId: [null]
    });
  }

  ngOnInit(): void {
    this.loadProducts();
    this.setupSearch();
    this.setupProductSelection();
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.filterCostCenters(searchTerm);
    });
  }

  private setupProductSelection(): void {
    this.productForm.get('selectedProductId')?.valueChanges.subscribe(productId => {
      if (productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
          this.selectedProduct = product;
          this.loadCostCentersWithLinkStatus(productId);
        }
      } else {
        this.selectedProduct = null;
        this.costCenters = [];
        this.filteredCostCenters = [];
      }
    });
  }

  private loadProducts(): void {
    this.isLoading = true;
    this.productService.getAll().subscribe({
      next: (products) => {
        this.products = products;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.snackBar.open('Error loading products', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  private loadCostCentersWithLinkStatus(productId: number): void {
    this.isLoading = true;
    this.productCostCenterLinkService.getCostCentersWithLinkStatus(productId, this.searchTerm).subscribe({
      next: (costCenters) => {
        this.costCenters = costCenters;
        this.filterCostCenters(this.searchTerm);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading cost centers:', error);
        this.snackBar.open('Error loading cost centers', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  private filterCostCenters(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredCostCenters = [...this.costCenters];
    } else {
      this.filteredCostCenters = this.costCenters.filter(cc => 
        cc.costCenterName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
  }

  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.searchSubject.next(searchTerm);
    
    // If we have a selected product, reload with search term
    if (this.selectedProduct) {
      this.loadCostCentersWithLinkStatus(this.selectedProduct.id);
    }
  }

  onLinkToggle(costCenter: CostCenterWithLink, isLinked: boolean): void {
    if (!this.selectedProduct) return;

    this.isSaving = true;
    const request: ToggleLinkRequest = {
      productId: this.selectedProduct.id,
      costCenterId: costCenter.costCenterId,
      isLinked: isLinked,
      minStock: costCenter.minStock,
      maxStock: costCenter.maxStock,
      reorderPoint: costCenter.reorderPoint
    };

    this.productCostCenterLinkService.toggleLink(request).subscribe({
      next: () => {
        costCenter.isLinked = isLinked;
        if (!isLinked) {
          costCenter.minStock = undefined;
          costCenter.maxStock = undefined;
          costCenter.reorderPoint = undefined;
        }
        this.snackBar.open(
          isLinked ? 'Cost center linked successfully' : 'Cost center unlinked successfully', 
          'Close', 
          { duration: 3000 }
        );
        this.isSaving = false;
      },
      error: (error) => {
        console.error('Error toggling link:', error);
        this.snackBar.open('Error updating link', 'Close', { duration: 3000 });
        this.isSaving = false;
      }
    });
  }

  onStockValueChange(costCenter: CostCenterWithLink, field: 'minStock' | 'maxStock' | 'reorderPoint', value: number | null): void {
    if (!this.selectedProduct || !costCenter.isLinked) return;

    costCenter[field] = value;

    // Auto-save the changes
    this.onLinkToggle(costCenter, true);
  }

  onMinStockChange(costCenter: CostCenterWithLink, event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.onStockValueChange(costCenter, 'minStock', value ? +value : null);
  }

  onMaxStockChange(costCenter: CostCenterWithLink, event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.onStockValueChange(costCenter, 'maxStock', value ? +value : null);
  }

  onReorderPointChange(costCenter: CostCenterWithLink, event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.onStockValueChange(costCenter, 'reorderPoint', value ? +value : null);
  }

  onCancel(): void {
    this.router.navigate(['/products/cost-center-links']);
  }

  get pageTitle(): string {
    return this.selectedProduct 
      ? `Product Cost Center Links - ${this.selectedProduct.code} - ${this.selectedProduct.name}`
      : 'Product Cost Center Links';
  }
}
