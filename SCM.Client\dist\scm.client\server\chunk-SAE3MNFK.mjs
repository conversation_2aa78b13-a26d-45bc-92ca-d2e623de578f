import './polyfills.server.mjs';
import{a as H,b as L}from"./chunk-J2FNDWGV.mjs";import{b as $,c as q}from"./chunk-GBVBP5JO.mjs";import{a as D,e as A,h as j,i as G}from"./chunk-KCBYLGU4.mjs";import{b as _,d as u,f as v,g as M,k as E,n as P,q as y,u as O,w as N}from"./chunk-R2YOBFRV.mjs";import{h as B}from"./chunk-PWFZPCXU.mjs";import{f as V}from"./chunk-NZ6MYAVT.mjs";import{U as F,_ as I,aa as T,ba as k,ca as R,da as w}from"./chunk-N6IFEGQ2.mjs";import{Fb as o,Fc as h,Hb as c,Ka as r,La as b,Lc as S,Qb as C,ab as p,cb as a,ha as x,lb as t,mb as e,nb as l,ub as f}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";function z(n,d){if(n&1&&(t(0,"mat-option",30),o(1),e()),n&2){let i=d.$implicit;a("value",i),r(),c(" ",i," ")}}function K(n,d){if(n&1&&(t(0,"mat-option",30),o(1),e()),n&2){let i=d.$implicit;a("value",i),r(),c(" ",i," ")}}function U(n,d){if(n&1&&(t(0,"mat-option",30),o(1),e()),n&2){let i=d.$implicit;a("value",i),r(),c(" ",i," ")}}function J(n,d){if(n&1&&(t(0,"mat-option",30),o(1),e()),n&2){let i=d.$implicit;a("value",i),r(),c(" ",i," ")}}var fe=(()=>{class n{constructor(i){this.fb=i,this.regions=["Egypt","Other Regions"],this.storeTypes=["Hotel","Restaurant","Retail"],this.taxRates=["None","5%","10%","15%","20%"],this.sellingPriceTargets=["Cost %","Retail %","Fixed"]}ngOnInit(){this.initForm()}initForm(){this.storeForm=this.fb.group({branchNo:["1",u.required],storeId:["1",u.required],branchName:["Casa Mare Resort",u.required],region:["Egypt"],storeType:["Hotel"],inHouseBarcodePrefix:["99"],documentPrefix:["RTLP"],taxRate:["None"],sellingPriceTarget:["Cost %"],address:[`K.450km Marsa Alam
Egypt
Alam
55555`],branchExportPath:[""],scaleExportPath:[""]})}saveStore(){this.storeForm.valid?console.log("Store configuration saved:",this.storeForm.value):console.log("Form is invalid")}deleteStore(){console.log("Delete store clicked")}uploadImage(){console.log("Upload image clicked")}static{this.\u0275fac=function(s){return new(s||n)(b(O))}}static{this.\u0275cmp=x({type:n,selectors:[["app-store-config"]],standalone:!0,features:[C],decls:97,vars:6,consts:[[1,"page-container"],[1,"page-header"],[1,"location-label"],[1,"branch-details-header"],[1,"store-form",3,"formGroup"],[1,"form-container"],[1,"form-section"],[1,"form-row"],["appearance","outline"],["matInput","","formControlName","branchNo"],["matInput","","formControlName","storeId"],["matInput","","formControlName","branchName"],["formControlName","region"],[3,"value",4,"ngFor","ngForOf"],["formControlName","storeType"],["matInput","","formControlName","inHouseBarcodePrefix"],["matInput","","formControlName","documentPrefix"],["formControlName","taxRate"],["formControlName","sellingPriceTarget"],[1,"image-upload-container"],[1,"image-placeholder"],["mat-icon-button","","color","primary",3,"click"],["appearance","outline",1,"full-width"],["matInput","","formControlName","address","rows","5"],["matInput","","formControlName","branchExportPath"],["mat-icon-button","","matSuffix",""],["matInput","","formControlName","scaleExportPath"],[1,"form-actions"],["mat-raised-button","","color","primary",3,"click","disabled"],["mat-raised-button","","color","warn",3,"click"],[3,"value"]],template:function(s,m){s&1&&(t(0,"div",0)(1,"div",1)(2,"h1"),o(3,"1 - Casa Mare Resort"),e(),t(4,"div",2),o(5,"All Locations"),e()(),t(6,"div",3)(7,"h2"),o(8,"BRANCH DETAILS"),e()(),t(9,"form",4)(10,"div",5)(11,"div",6)(12,"h3"),o(13,"General"),e(),t(14,"div",7)(15,"mat-form-field",8)(16,"mat-label"),o(17,"Branch No"),e(),l(18,"input",9),e()(),t(19,"div",7)(20,"mat-form-field",8)(21,"mat-label"),o(22,"Store ID"),e(),l(23,"input",10),e()(),t(24,"div",7)(25,"mat-form-field",8)(26,"mat-label"),o(27,"Branch Name"),e(),l(28,"input",11),e()(),t(29,"div",7)(30,"mat-form-field",8)(31,"mat-label"),o(32,"Region"),e(),t(33,"mat-select",12),p(34,z,2,2,"mat-option",13),e()()(),t(35,"div",7)(36,"mat-form-field",8)(37,"mat-label"),o(38,"Store Type"),e(),t(39,"mat-select",14),p(40,K,2,2,"mat-option",13),e()()(),t(41,"div",7)(42,"mat-form-field",8)(43,"mat-label"),o(44,"Inhouse Barcode Prefix"),e(),l(45,"input",15),e()(),t(46,"div",7)(47,"mat-form-field",8)(48,"mat-label"),o(49,"Document Prefix"),e(),l(50,"input",16),e()(),t(51,"div",7)(52,"mat-form-field",8)(53,"mat-label"),o(54,"Tax Rate"),e(),t(55,"mat-select",17),p(56,U,2,2,"mat-option",13),e()()(),t(57,"div",7)(58,"mat-form-field",8)(59,"mat-label"),o(60,"Selling Price Target"),e(),t(61,"mat-select",18),p(62,J,2,2,"mat-option",13),e()()()(),t(63,"div",6)(64,"div",19)(65,"div",20)(66,"span"),o(67,"Click to add Picture"),e(),t(68,"button",21),f("click",function(){return m.uploadImage()}),t(69,"mat-icon"),o(70,"add_a_photo"),e()()()(),t(71,"div",7)(72,"mat-form-field",22)(73,"mat-label"),o(74,"Address"),e(),l(75,"textarea",23),e()(),t(76,"div",7)(77,"mat-form-field",22)(78,"mat-label"),o(79,"Branch Export Path"),e(),l(80,"input",24),t(81,"button",25)(82,"mat-icon"),o(83,"folder"),e()()()(),t(84,"div",7)(85,"mat-form-field",22)(86,"mat-label"),o(87,"Scale Export Path"),e(),l(88,"input",26),t(89,"button",25)(90,"mat-icon"),o(91,"folder"),e()()()()()(),t(92,"div",27)(93,"button",28),f("click",function(){return m.saveStore()}),o(94," Settings "),e(),t(95,"button",29),f("click",function(){return m.deleteStore()}),o(96," Delete "),e()()()()),s&2&&(r(9),a("formGroup",m.storeForm),r(25),a("ngForOf",m.regions),r(6),a("ngForOf",m.storeTypes),r(16),a("ngForOf",m.taxRates),r(6),a("ngForOf",m.sellingPriceTargets),r(31),a("disabled",m.storeForm.invalid))},dependencies:[S,h,N,E,_,v,M,P,y,k,I,T,B,G,j,D,A,w,R,q,$,L,H,F,V],styles:[".page-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:4px;box-shadow:0 2px 4px #0000001a;padding:20px;margin-bottom:20px}.page-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px;padding-bottom:10px;border-bottom:1px solid #e0e0e0}.location-label[_ngcontent-%COMP%]{font-weight:700;color:#1976d2}.branch-details-header[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:10px;margin-bottom:20px;text-align:center;border-radius:4px}.branch-details-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:18px;color:#333}.store-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.form-container[_ngcontent-%COMP%]{display:flex;gap:30px}.form-section[_ngcontent-%COMP%]{flex:1}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-top:0;margin-bottom:16px;color:#1976d2;border-bottom:1px solid #e0e0e0;padding-bottom:8px}.form-row[_ngcontent-%COMP%]{margin-bottom:16px}.full-width[_ngcontent-%COMP%]{width:100%}.image-upload-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:20px}.image-placeholder[_ngcontent-%COMP%]{width:150px;height:150px;border:2px dashed #ccc;display:flex;flex-direction:column;justify-content:center;align-items:center;cursor:pointer;border-radius:4px;transition:all .3s ease}.image-placeholder[_ngcontent-%COMP%]:hover{border-color:#1976d2;background-color:#1976d20d}.image-placeholder[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{margin-bottom:8px;color:#757575}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:10px;margin-top:20px}@media (max-width: 768px){.form-container[_ngcontent-%COMP%]{flex-direction:column}}"]})}}return n})();export{fe as StoreConfigComponent};
