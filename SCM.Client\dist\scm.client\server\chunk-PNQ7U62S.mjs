import './polyfills.server.mjs';
import{a as s}from"./chunk-I44XXN6Y.mjs";import{Z as r,ca as a}from"./chunk-FFIKRRNO.mjs";var c=(()=>{class e{constructor(t){this.apiService=t,this.path="unitgroups"}getAll(){return this.apiService.get(this.path)}getById(t){return this.apiService.get(`${this.path}/${t}`)}getWithUnits(t){return this.apiService.get(`${this.path}/${t}/with-units`)}create(t){return this.apiService.post(this.path,t)}update(t,i){return this.apiService.put(`${this.path}/${t}`,i)}delete(t){return this.apiService.delete(`${this.path}/${t}`)}static{this.\u0275fac=function(i){return new(i||e)(a(s))}}static{this.\u0275prov=r({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();export{c as a};
