import './polyfills.server.mjs';
import{a as s}from"./chunk-I44XXN6Y.mjs";import{Z as r,ca as a}from"./chunk-FFIKRRNO.mjs";var c=(()=>{class i{constructor(t){this.apiService=t,this.path="recipes"}getAll(){return this.apiService.get(this.path)}getById(t){return this.apiService.get(`${this.path}/${t}`)}getByProductId(t){return this.apiService.get(`${this.path}/product/${t}`)}getActive(){return this.apiService.get(`${this.path}/active`)}getSubRecipes(){return this.apiService.get(`${this.path}/subrecipes`)}create(t){return this.apiService.post(this.path,t)}update(t,e){return this.apiService.put(`${this.path}/${t}`,e)}delete(t){return this.apiService.delete(`${this.path}/${t}`)}addIngredient(t,e){return this.apiService.post(`${this.path}/${t}/ingredients`,e)}updateIngredient(t,e){return this.apiService.put(`${this.path}/ingredients/${t}`,e)}removeIngredient(t,e){return this.apiService.delete(`${this.path}/${t}/ingredients/${e}`)}calculateCost(t){return this.apiService.get(`${this.path}/${t}/calculate-cost`)}static{this.\u0275fac=function(e){return new(e||i)(a(s))}}static{this.\u0275prov=r({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();export{c as a};
