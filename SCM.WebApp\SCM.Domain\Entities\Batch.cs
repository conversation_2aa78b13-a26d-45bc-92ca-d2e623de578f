using SCM.Domain.Common;

namespace SCM.Domain.Entities;

public class Batch : BaseEntity
{
    public string BatchNumber { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public int? UnitId { get; set; }
    public DateTime? ManufactureDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual Product Product { get; set; } = null!;
    public virtual Unit? Unit { get; set; }
    public virtual ICollection<TransactionDetail> TransactionDetails { get; set; } = new List<TransactionDetail>();
    public virtual ICollection<StockTakeDetail> StockTakeDetails { get; set; } = new List<StockTakeDetail>();
}
