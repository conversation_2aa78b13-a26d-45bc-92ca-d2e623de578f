import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ProductCostCenterLinkService } from '../../../core/services/product-cost-center-link.service';
import { ProductService } from '../../../core/services/product.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { Product } from '../../../core/models/product.model';
import { CostCenter } from '../../../core/models/cost-center.model';
import { ProductCostCenterLink, CreateProductCostCenterLink, UpdateProductCostCenterLink } from '../../../core/models/product-cost-center-link.model';
import { forkJoin, catchError, of } from 'rxjs';

@Component({
  selector: 'app-product-cost-center-link-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    RouterModule
  ],
  templateUrl: './product-cost-center-link-detail.component.html',
  styleUrls: ['./product-cost-center-link-detail.component.scss']
})
export class ProductCostCenterLinkDetailComponent implements OnInit {
  linkForm!: FormGroup;
  isEditMode: boolean = false;
  linkId: string | null = null;
  isLoading: boolean = false;
  isSaving: boolean = false;

  // Data from API
  products: Product[] = [];
  costCenters: CostCenter[] = [];
  currentLink: ProductCostCenterLink | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private productCostCenterLinkService: ProductCostCenterLinkService,
    private productService: ProductService,
    private costCenterService: CostCenterService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadReferenceData();

    this.route.paramMap.subscribe(params => {
      this.linkId = params.get('id');
      if (this.linkId) {
        this.isEditMode = true;
        this.loadLinkData(parseInt(this.linkId));
      }
    });
  }

  initForm(): void {
    this.linkForm = this.fb.group({
      productId: [null, Validators.required],
      costCenterId: [null, Validators.required],
      minStock: [null, [Validators.min(0)]],
      maxStock: [null, [Validators.min(0)]],
      reorderPoint: [null, [Validators.min(0)]],
      autoReorder: [false]
    });

    // Add custom validator to ensure maxStock >= minStock
    this.linkForm.addValidators(this.stockRangeValidator.bind(this));
  }

  stockRangeValidator(control: AbstractControl): ValidationErrors | null {
    const form = control as FormGroup;
    const minStock = form.get('minStock')?.value;
    const maxStock = form.get('maxStock')?.value;

    if (minStock != null && maxStock != null && minStock > maxStock) {
      return { stockRangeInvalid: true };
    }
    return null;
  }

  loadReferenceData(): void {
    this.isLoading = true;

    forkJoin({
      products: this.productService.getAll(),
      costCenters: this.costCenterService.getAllCostCenters()
    }).pipe(
      catchError(error => {
        console.error('Error loading reference data', error);
        this.snackBar.open('Error loading data. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        return of({ products: [], costCenters: [] });
      })
    ).subscribe(data => {
      this.products = data.products;
      this.costCenters = data.costCenters;
      this.isLoading = false;
    });
  }

  loadLinkData(id: number): void {
    this.productCostCenterLinkService.getById(id).pipe(
      catchError(error => {
        console.error('Error loading link data', error);
        this.snackBar.open('Error loading link data. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/products/cost-center-links']);
        return of(null);
      })
    ).subscribe(link => {
      if (link) {
        this.currentLink = link;
        this.linkForm.patchValue({
          productId: link.productId,
          costCenterId: link.costCenterId,
          minStock: link.minStock,
          maxStock: link.maxStock,
          reorderPoint: link.reorderPoint,
          autoReorder: link.autoReorder
        });
      }
    });
  }

  onSubmit(): void {
    if (this.linkForm.valid) {
      this.isSaving = true;
      const formData = this.linkForm.value;

      if (this.isEditMode && this.currentLink) {
        const updateData: UpdateProductCostCenterLink = {
          id: this.currentLink.id,
          productId: formData.productId,
          costCenterId: formData.costCenterId,
          minStock: formData.minStock,
          maxStock: formData.maxStock,
          reorderPoint: formData.reorderPoint,
          autoReorder: formData.autoReorder,
          isActive: true
        };

        this.productCostCenterLinkService.update(this.currentLink.id, updateData).pipe(
          catchError(error => {
            console.error('Error updating link', error);
            this.snackBar.open('Error updating link. Please try again.', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            return of(null);
          })
        ).subscribe(() => {
          this.isSaving = false;
          this.snackBar.open('Link updated successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/products/cost-center-links']);
        });
      } else {
        const createData: CreateProductCostCenterLink = {
          productId: formData.productId,
          costCenterId: formData.costCenterId,
          minStock: formData.minStock,
          maxStock: formData.maxStock,
          reorderPoint: formData.reorderPoint,
          autoReorder: formData.autoReorder
        };

        this.productCostCenterLinkService.create(createData).pipe(
          catchError(error => {
            console.error('Error creating link', error);
            this.snackBar.open('Error creating link. Please try again.', 'Close', {
              duration: 5000,
              panelClass: ['error-snackbar']
            });
            return of(null);
          })
        ).subscribe(() => {
          this.isSaving = false;
          this.snackBar.open('Link created successfully', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/products/cost-center-links']);
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.router.navigate(['/products/cost-center-links']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.linkForm.controls).forEach(key => {
      const control = this.linkForm.get(key);
      control?.markAsTouched();
    });
  }

  getProductDisplayName(product: Product): string {
    return `${product.code} - ${product.name}`;
  }

  getCostCenterDisplayName(costCenter: CostCenter): string {
    return costCenter.name;
  }

  get pageTitle(): string {
    return this.isEditMode ? 'Edit Product Cost Center Link' : 'Add Product Cost Center Link';
  }

  get submitButtonText(): string {
    return this.isEditMode ? 'Update Link' : 'Create Link';
  }
}
