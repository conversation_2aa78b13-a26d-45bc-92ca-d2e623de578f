import './polyfills.server.mjs';
import{a as p}from"./chunk-A3ICGYXF.mjs";import{Sc as n,Z as s,ca as r}from"./chunk-FFIKRRNO.mjs";var l=(()=>{class i{constructor(t){this.http=t,this.apiUrl=`${p.apiUrl}/stock-adjustments`}getAllStockAdjustments(){return this.http.get(this.apiUrl)}getStockAdjustmentById(t){return this.http.get(`${this.apiUrl}/${t}`)}getStockAdjustmentsByCostCenterId(t){return this.http.get(`${this.apiUrl}/cost-center/${t}`)}getStockAdjustmentsByStatus(t){return this.http.get(`${this.apiUrl}/status/${t}`)}createStockAdjustment(t){return this.http.post(this.apiUrl,t)}updateStockAdjustment(t,e){return this.http.put(`${this.apiUrl}/${t}`,e)}deleteStockAdjustment(t){return this.http.delete(`${this.apiUrl}/${t}`)}completeStockAdjustment(t,e){return this.http.post(`${this.apiUrl}/${t}/complete`,e)}cancelStockAdjustment(t){return this.http.post(`${this.apiUrl}/${t}/cancel`,{})}getStockAdjustmentDetails(t){return this.http.get(`${this.apiUrl}/${t}/details`)}getStockAdjustmentDetailById(t){return this.http.get(`${this.apiUrl}/details/${t}`)}createStockAdjustmentDetail(t,e){return this.http.post(`${this.apiUrl}/${t}/details`,e)}updateStockAdjustmentDetail(t,e){return this.http.put(`${this.apiUrl}/details/${t}`,e)}deleteStockAdjustmentDetail(t){return this.http.delete(`${this.apiUrl}/details/${t}`)}static{this.\u0275fac=function(e){return new(e||i)(r(n))}}static{this.\u0275prov=s({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();export{l as a};
