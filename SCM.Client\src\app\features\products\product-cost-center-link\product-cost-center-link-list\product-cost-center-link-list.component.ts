import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router, RouterModule } from '@angular/router';
import { ProductCostCenterLinkService } from '../../../../core/services/product-cost-center-link.service';
import { ProductService } from '../../../../core/services/product.service';
import { CostCenterService } from '../../../../core/services/cost-center.service';
import { ProductCostCenterLinkListItem } from '../../../../core/models/product-cost-center-link.model';
import { Product } from '../../../../core/models/product.model';
import { CostCenter } from '../../../../core/models/cost-center.model';
import { catchError, of } from 'rxjs';

@Component({
  selector: 'app-product-cost-center-link-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTableModule,
    MatSortModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    RouterModule
  ],
  templateUrl: './product-cost-center-link-list.component.html',
  styleUrls: ['./product-cost-center-link-list.component.scss']
})
export class ProductCostCenterLinkListComponent implements OnInit {
  displayedColumns: string[] = ['productCode', 'productName', 'costCenterName', 'minStock', 'maxStock', 'reorderPoint', 'actions'];
  links: ProductCostCenterLinkListItem[] = [];
  filteredLinks: ProductCostCenterLinkListItem[] = [];
  products: Product[] = [];
  costCenters: CostCenter[] = [];

  searchTerm: string = '';
  selectedProductId: number | null = null;
  selectedCostCenterId: number | null = null;
  isLoading: boolean = false;

  constructor(
    private productCostCenterLinkService: ProductCostCenterLinkService,
    private productService: ProductService,
    private costCenterService: CostCenterService,
    private snackBar: MatSnackBar,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isLoading = true;

    // Load all data in parallel
    Promise.all([
      this.productCostCenterLinkService.getAll().toPromise(),
      this.productService.getAll().toPromise(),
      this.costCenterService.getAllCostCenters().toPromise()
    ]).then(([links, products, costCenters]) => {
      this.links = links || [];
      this.products = products || [];
      this.costCenters = costCenters || [];
      this.applyFilters();
      this.isLoading = false;
    }).catch(error => {
      console.error('Error loading data:', error);
      this.snackBar.open('Error loading data. Please try again.', 'Close', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
      this.isLoading = false;
    });
  }

  applyFilters(): void {
    let filtered = [...this.links];

    // Apply search term filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(link =>
        link.productCode.toLowerCase().includes(searchLower) ||
        link.productName.toLowerCase().includes(searchLower) ||
        link.costCenterName.toLowerCase().includes(searchLower)
      );
    }

    // Apply product filter
    if (this.selectedProductId) {
      filtered = filtered.filter(link => link.productId === this.selectedProductId);
    }

    // Apply cost center filter
    if (this.selectedCostCenterId) {
      filtered = filtered.filter(link => link.costCenterId === this.selectedCostCenterId);
    }

    this.filteredLinks = filtered;
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onProductFilterChange(): void {
    this.applyFilters();
  }

  onCostCenterFilterChange(): void {
    this.applyFilters();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedProductId = null;
    this.selectedCostCenterId = null;
    this.applyFilters();
  }

  addLink(): void {
    this.router.navigate(['/products/cost-center-links/new']);
  }

  openManager(): void {
    this.router.navigate(['/products/cost-center-links/manager']);
  }

  editLink(id: number): void {
    this.router.navigate(['/products/cost-center-links', id]);
  }

  deleteLink(id: number): void {
    if (confirm('Are you sure you want to delete this product cost center link?')) {
      this.productCostCenterLinkService.delete(id).pipe(
        catchError(error => {
          console.error('Error deleting link', error);
          this.snackBar.open('Error deleting link. Please try again.', 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          return of(null);
        })
      ).subscribe(() => {
        this.snackBar.open('Link deleted successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadData(); // Reload the data
      });
    }
  }

  formatNumber(value: number | null | undefined): string {
    return value != null ? value.toString() : '-';
  }
}
