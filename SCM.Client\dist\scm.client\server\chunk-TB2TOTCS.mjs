import './polyfills.server.mjs';
import{b as Z,c as $}from"./chunk-GBVBP5JO.mjs";import{a as J,b as K,d as Q,e as W,h as X,i as Y}from"./chunk-KCBYLGU4.mjs";import{b as I,d as h,f as w,g as F,k as E,n as k,q as O,u as B,w as j}from"./chunk-R2YOBFRV.mjs";import{a as nt}from"./chunk-2IOWMPES.mjs";import{a as it,b as rt}from"./chunk-S2DPCQFA.mjs";import{a as tt,b as et}from"./chunk-4QI52JLP.mjs";import{a as A,b as D,c as G,e as V,f as z,h as H}from"./chunk-PWFZPCXU.mjs";import"./chunk-A3ICGYXF.mjs";import{_ as N,aa as T,ba as q,ca as U,da as R}from"./chunk-N6IFEGQ2.mjs";import{b as x,e as y,f as P,i as L}from"./chunk-46VHTMVE.mjs";import{Fb as n,Gb as _,Gc as S,Ka as o,La as l,Lc as b,Qb as v,Rb as C,ab as u,cb as a,ha as M,lb as t,mb as e,nb as c,ub as g}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";var ot=()=>["/register"];function at(r,f){r&1&&(t(0,"mat-error"),n(1," Username is required "),e())}function mt(r,f){r&1&&(t(0,"mat-error"),n(1," Password is required "),e())}function st(r,f){r&1&&c(0,"mat-spinner",16)}function lt(r,f){r&1&&(t(0,"span"),n(1,"Login"),e())}var Ot=(()=>{class r{constructor(d,m,i,s,p){this.formBuilder=d,this.authService=m,this.router=i,this.route=s,this.snackBar=p,this.isLoading=!1,this.hidePassword=!0,this.returnUrl="/"}ngOnInit(){this.initForm(),this.returnUrl=this.route.snapshot.queryParams.returnUrl||"/",this.authService.isAuthenticated()&&this.router.navigate([this.returnUrl])}initForm(){this.loginForm=this.formBuilder.group({username:["",[h.required]],password:["",[h.required]]})}onSubmit(){this.loginForm.invalid||(this.isLoading=!0,this.authService.login(this.loginForm.value).subscribe({next:()=>{this.router.navigate([this.returnUrl])},error:d=>{this.isLoading=!1,this.snackBar.open(d.message||"Login failed","Close",{duration:5e3,panelClass:["error-snackbar"]})},complete:()=>{this.isLoading=!1}}))}static{this.\u0275fac=function(m){return new(m||r)(l(B),l(nt),l(y),l(x),l(tt))}}static{this.\u0275cmp=M({type:r,selectors:[["app-login"]],standalone:!0,features:[v],decls:37,vars:10,consts:[[1,"login-container"],[1,"login-card-wrapper"],[1,"login-card"],[3,"ngSubmit","formGroup"],[1,"form-field"],["appearance","outline"],["matInput","","formControlName","username","autocomplete","username"],["matPrefix",""],[4,"ngIf"],["matInput","","formControlName","password","autocomplete","current-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],[1,"register-link"],[3,"routerLink"],["diameter","20"]],template:function(m,i){if(m&1&&(t(0,"div",0)(1,"div",1)(2,"mat-card",2)(3,"mat-card-header")(4,"mat-card-title"),n(5,"Login"),e()(),t(6,"mat-card-content")(7,"form",3),g("ngSubmit",function(){return i.onSubmit()}),t(8,"div",4)(9,"mat-form-field",5)(10,"mat-label"),n(11,"Username"),e(),c(12,"input",6),t(13,"mat-icon",7),n(14,"person"),e(),u(15,at,2,0,"mat-error",8),e()(),t(16,"div",4)(17,"mat-form-field",5)(18,"mat-label"),n(19,"Password"),e(),c(20,"input",9),t(21,"mat-icon",7),n(22,"lock"),e(),t(23,"button",10),g("click",function(){return i.hidePassword=!i.hidePassword}),t(24,"mat-icon"),n(25),e()(),u(26,mt,2,0,"mat-error",8),e()(),t(27,"div",11)(28,"button",12),u(29,st,1,0,"mat-spinner",13)(30,lt,2,0,"span",8),e()()()(),t(31,"mat-card-actions")(32,"div",14)(33,"span"),n(34,"Don't have an account?"),e(),t(35,"a",15),n(36,"Register"),e()()()()()()),m&2){let s,p;o(7),a("formGroup",i.loginForm),o(8),a("ngIf",(s=i.loginForm.get("username"))==null?null:s.hasError("required")),o(5),a("type",i.hidePassword?"password":"text"),o(5),_(i.hidePassword?"visibility_off":"visibility"),o(),a("ngIf",(p=i.loginForm.get("password"))==null?null:p.hasError("required")),o(2),a("disabled",i.loginForm.invalid||i.isLoading),o(),a("ngIf",i.isLoading),o(),a("ngIf",!i.isLoading),o(5),a("routerLink",C(9,ot))}},dependencies:[b,S,j,E,I,w,F,k,O,L,P,q,N,T,H,A,V,G,z,D,Y,X,J,K,Q,W,$,Z,R,U,et,rt,it],styles:[".login-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background-color:#f5f5f5}.login-card-wrapper[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px}.login-card[_ngcontent-%COMP%]{width:100%}mat-card-header[_ngcontent-%COMP%]{margin-bottom:20px}mat-card-title[_ngcontent-%COMP%]{font-size:24px;margin-bottom:0}.form-field[_ngcontent-%COMP%]{margin-bottom:16px}mat-form-field[_ngcontent-%COMP%]{width:100%}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:24px;margin-bottom:16px}button[type=submit][_ngcontent-%COMP%]{width:100%;height:40px}.register-link[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:8px;margin-top:8px;margin-bottom:8px}mat-spinner[_ngcontent-%COMP%]{display:inline-block;margin-right:8px}"]})}}return r})();export{Ot as LoginComponent};
