import './polyfills.server.mjs';
import{a as Rt}from"./chunk-FQAMTFAR.mjs";import{c as Ft}from"./chunk-F4JTRPZF.mjs";import{a as M,b as bt,c as b,d as wt,e as vt}from"./chunk-4JYGXCOT.mjs";import{c as Et,h as Pt}from"./chunk-3LTRCH3K.mjs";import{a as xt,b as Mt}from"./chunk-CHXIZKOG.mjs";import{a as Tt,b as ht}from"./chunk-J2FNDWGV.mjs";import{b as yt,c as Lt}from"./chunk-GBVBP5JO.mjs";import{a as Ct,e as gt,h as St,i as kt}from"./chunk-KCBYLGU4.mjs";import{b as Q,f as W,j as U,v as q,w as G}from"./chunk-R2YOBFRV.mjs";import{d as Nt}from"./chunk-TMTYBZNO.mjs";import{a as rt,b as ot,c as at,d as st,e as ct,f as lt,g as mt,h as dt,i as ft,j as pt,k as _t,l as ut}from"./chunk-CUT3Q574.mjs";import{a as It,b as Vt}from"./chunk-S2DPCQFA.mjs";import{a as Dt,b as Ot}from"./chunk-4QI52JLP.mjs";import{a as et,c as nt,h as it}from"./chunk-PWFZPCXU.mjs";import"./chunk-A3ICGYXF.mjs";import{U as J,_ as K,aa as X,ba as Y,ca as Z,da as tt}from"./chunk-N6IFEGQ2.mjs";import{e as H,i as j}from"./chunk-46VHTMVE.mjs";import{Bb as w,Cb as v,Db as y,Ec as R,Fb as s,Fc as B,Gb as x,Gc as $,Hb as L,J as k,Jc as z,Ka as l,La as T,Lc as A,Mb as E,Nb as P,Ob as D,Qb as I,Rb as V,Wb as N,Yb as F,ab as _,cb as d,ha as O,lb as n,mb as i,nb as h,oa as f,ob as C,pa as p,pb as g,rb as S,ub as u,wb as m}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";var Bt=()=>[10,25,50,100];function $t(e,a){e&1&&(n(0,"div",6),h(1,"mat-spinner",7),i())}function zt(e,a){if(e&1&&(n(0,"mat-option",17),s(1),i()),e&2){let t=a.$implicit;d("value",t),l(),L(" ",t," ")}}function At(e,a){if(e&1){let t=S();n(0,"mat-card",8)(1,"mat-card-content")(2,"div",9)(3,"mat-form-field",10)(4,"mat-label"),s(5,"Search"),i(),n(6,"input",11),D("ngModelChange",function(r){f(t);let c=m();return P(c.searchTerm,r)||(c.searchTerm=r),p(r)}),u("keyup",function(){f(t);let r=m();return p(r.applyFilter())}),i(),n(7,"mat-icon",12),s(8,"search"),i()(),n(9,"mat-form-field",10)(10,"mat-label"),s(11,"Status"),i(),n(12,"mat-select",13),D("ngModelChange",function(r){f(t);let c=m();return P(c.selectedStatus,r)||(c.selectedStatus=r),p(r)}),u("selectionChange",function(){f(t);let r=m();return p(r.applyFilter())}),n(13,"mat-option",14),s(14,"All"),i(),_(15,zt,2,2,"mat-option",15),i()(),n(16,"button",16),u("click",function(){f(t);let r=m();return p(r.resetFilters())}),n(17,"mat-icon"),s(18,"clear"),i(),s(19," Reset "),i()()()()}if(e&2){let t=m();l(6),E("ngModel",t.searchTerm),l(6),E("ngModel",t.selectedStatus),l(3),d("ngForOf",t.statusOptions)}}function Ht(e,a){e&1&&(n(0,"th",33),s(1,"Reference #"),i())}function jt(e,a){if(e&1&&(n(0,"td",34),s(1),i()),e&2){let t=a.$implicit;l(),x(t.referenceNumber)}}function Qt(e,a){e&1&&(n(0,"th",33),s(1,"From"),i())}function Wt(e,a){if(e&1&&(n(0,"td",34),s(1),i()),e&2){let t=a.$implicit;l(),x(t.fromCostCenterName)}}function Ut(e,a){e&1&&(n(0,"th",33),s(1,"To"),i())}function qt(e,a){if(e&1&&(n(0,"td",34),s(1),i()),e&2){let t=a.$implicit;l(),x(t.toCostCenterName)}}function Gt(e,a){e&1&&(n(0,"th",33),s(1,"Date"),i())}function Jt(e,a){if(e&1&&(n(0,"td",34),s(1),N(2,"date"),i()),e&2){let t=a.$implicit;l(),x(F(2,1,t.transferDate,"medium"))}}function Kt(e,a){e&1&&(n(0,"th",33),s(1,"Status"),i())}function Xt(e,a){if(e&1&&(n(0,"td",34)(1,"span",35),s(2),i()()),e&2){let t=a.$implicit,o=m(2);l(),d("ngClass",o.getStatusClass(t.status)),l(),L(" ",t.status," ")}}function Yt(e,a){e&1&&(n(0,"th",36),s(1,"Actions"),i())}function Zt(e,a){if(e&1){let t=S();n(0,"button",42),u("click",function(){f(t);let r=m().$implicit,c=m(2);return p(c.editStockTransfer(r))}),n(1,"mat-icon"),s(2,"edit"),i()()}}function te(e,a){if(e&1){let t=S();n(0,"button",43),u("click",function(){f(t);let r=m().$implicit,c=m(2);return p(c.completeStockTransfer(r))}),n(1,"mat-icon"),s(2,"check_circle"),i()()}}function ee(e,a){if(e&1){let t=S();n(0,"button",44),u("click",function(){f(t);let r=m().$implicit,c=m(2);return p(c.cancelStockTransfer(r))}),n(1,"mat-icon"),s(2,"cancel"),i()()}}function ne(e,a){if(e&1){let t=S();n(0,"button",45),u("click",function(){f(t);let r=m().$implicit,c=m(2);return p(c.deleteStockTransfer(r))}),n(1,"mat-icon"),s(2,"delete"),i()()}}function ie(e,a){if(e&1){let t=S();n(0,"td",34)(1,"button",37),u("click",function(){let r=f(t).$implicit,c=m(2);return p(c.viewStockTransfer(r))}),n(2,"mat-icon"),s(3,"visibility"),i()(),_(4,Zt,3,0,"button",38)(5,te,3,0,"button",39)(6,ee,3,0,"button",40)(7,ne,3,0,"button",41),i()}if(e&2){let t=a.$implicit;l(4),d("ngIf",t.status==="Draft"),l(),d("ngIf",t.status==="Draft"||t.status==="Pending"),l(),d("ngIf",t.status==="Draft"||t.status==="Pending"),l(),d("ngIf",t.status==="Draft")}}function re(e,a){e&1&&h(0,"tr",46)}function oe(e,a){e&1&&h(0,"tr",47)}function ae(e,a){e&1&&(n(0,"tr",48)(1,"td",49)(2,"div",50)(3,"mat-icon"),s(4,"info"),i(),n(5,"span"),s(6,"No stock transfers found"),i()()()())}function se(e,a){if(e&1&&(n(0,"div",18)(1,"table",19),C(2,20),_(3,Ht,2,0,"th",21)(4,jt,2,1,"td",22),g(),C(5,23),_(6,Qt,2,0,"th",21)(7,Wt,2,1,"td",22),g(),C(8,24),_(9,Ut,2,0,"th",21)(10,qt,2,1,"td",22),g(),C(11,25),_(12,Gt,2,0,"th",21)(13,Jt,3,4,"td",22),g(),C(14,26),_(15,Kt,2,0,"th",21)(16,Xt,3,2,"td",22),g(),C(17,27),_(18,Yt,2,0,"th",28)(19,ie,8,4,"td",22),g(),_(20,re,1,0,"tr",29)(21,oe,1,0,"tr",30)(22,ae,7,0,"tr",31),i(),h(23,"mat-paginator",32),i()),e&2){let t=m();l(),d("dataSource",t.filteredTransfers),l(19),d("matHeaderRowDef",t.displayedColumns),l(),d("matRowDefColumns",t.displayedColumns),l(2),d("pageSizeOptions",V(4,Bt))}}var Ue=(()=>{class e{constructor(t,o,r,c){this.stockTransferService=t,this.router=o,this.snackBar=r,this.dialog=c,this.displayedColumns=["referenceNumber","fromCostCenterName","toCostCenterName","transferDate","status","actions"],this.stockTransfers=[],this.filteredTransfers=[],this.isLoading=!1,this.searchTerm="",this.selectedStatus="",this.statusOptions=["All","Draft","Pending","Completed","Cancelled"]}ngOnInit(){this.loadStockTransfers()}loadStockTransfers(){this.isLoading=!0,this.stockTransferService.getAllStockTransfers().pipe(k(()=>this.isLoading=!1)).subscribe({next:t=>{this.stockTransfers=t,this.filteredTransfers=[...this.stockTransfers]},error:t=>{console.error("Error loading stock transfers",t),this.snackBar.open("Error loading stock transfers","Close",{duration:3e3})}})}applyFilter(){this.filteredTransfers=this.stockTransfers.filter(t=>{let o=this.searchTerm===""||t.referenceNumber.toLowerCase().includes(this.searchTerm.toLowerCase())||t.fromCostCenterName.toLowerCase().includes(this.searchTerm.toLowerCase())||t.toCostCenterName.toLowerCase().includes(this.searchTerm.toLowerCase()),r=this.selectedStatus==="All"||this.selectedStatus===""||t.status===this.selectedStatus;return o&&r})}resetFilters(){this.searchTerm="",this.selectedStatus="",this.filteredTransfers=[...this.stockTransfers]}addStockTransfer(){this.router.navigate(["/inventory/stock-transfers/new"])}editStockTransfer(t){this.router.navigate(["/inventory/stock-transfers",t.id])}viewStockTransfer(t){this.router.navigate(["/inventory/stock-transfers",t.id])}deleteStockTransfer(t){confirm(`Are you sure you want to delete stock transfer ${t.referenceNumber}?`)&&(this.isLoading=!0,this.stockTransferService.deleteStockTransfer(t.id).pipe(k(()=>this.isLoading=!1)).subscribe({next:()=>{this.snackBar.open("Stock transfer deleted successfully","Close",{duration:3e3}),this.loadStockTransfers()},error:o=>{console.error("Error deleting stock transfer",o),this.snackBar.open("Error deleting stock transfer: "+(o.error||"Unknown error"),"Close",{duration:5e3})}}))}completeStockTransfer(t){confirm(`Are you sure you want to complete stock transfer ${t.referenceNumber}? This will update inventory levels.`)&&(this.isLoading=!0,this.stockTransferService.completeStockTransfer(t.id,{id:t.id}).pipe(k(()=>this.isLoading=!1)).subscribe({next:()=>{this.snackBar.open("Stock transfer completed successfully","Close",{duration:3e3}),this.loadStockTransfers()},error:o=>{console.error("Error completing stock transfer",o),this.snackBar.open("Error completing stock transfer: "+(o.error||"Unknown error"),"Close",{duration:5e3})}}))}cancelStockTransfer(t){confirm(`Are you sure you want to cancel stock transfer ${t.referenceNumber}?`)&&(this.isLoading=!0,this.stockTransferService.cancelStockTransfer(t.id).pipe(k(()=>this.isLoading=!1)).subscribe({next:()=>{this.snackBar.open("Stock transfer cancelled successfully","Close",{duration:3e3}),this.loadStockTransfers()},error:o=>{console.error("Error cancelling stock transfer",o),this.snackBar.open("Error cancelling stock transfer: "+(o.error||"Unknown error"),"Close",{duration:5e3})}}))}getStatusClass(t){switch(t){case"Draft":return"status-draft";case"Pending":return"status-pending";case"Completed":return"status-completed";case"Cancelled":return"status-cancelled";default:return""}}static{this.\u0275fac=function(o){return new(o||e)(T(Rt),T(H),T(Dt),T(Et))}}static{this.\u0275cmp=O({type:e,selectors:[["app-stock-transfer-list"]],viewQuery:function(o,r){if(o&1&&(w(M,5),w(b,5)),o&2){let c;v(c=y())&&(r.paginator=c.first),v(c=y())&&(r.sort=c.first)}},standalone:!0,features:[I],decls:11,vars:3,consts:[[1,"container"],[1,"header"],["mat-raised-button","","color","primary",3,"click"],["class","loading-spinner",4,"ngIf"],["class","filter-card",4,"ngIf"],["class","table-container mat-elevation-z2",4,"ngIf"],[1,"loading-spinner"],["diameter","40"],[1,"filter-card"],[1,"filter-container"],["appearance","outline"],["matInput","","placeholder","Search by reference number or cost center",3,"ngModelChange","keyup","ngModel"],["matSuffix",""],[3,"ngModelChange","selectionChange","ngModel"],["value",""],[3,"value",4,"ngFor","ngForOf"],["mat-button","","color","primary",3,"click"],[3,"value"],[1,"table-container","mat-elevation-z2"],["mat-table","","matSort","",1,"stock-transfer-table",3,"dataSource"],["matColumnDef","referenceNumber"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","fromCostCenterName"],["matColumnDef","toCostCenterName"],["matColumnDef","transferDate"],["matColumnDef","status"],["matColumnDef","actions"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["class","mat-row",4,"matNoDataRow"],["showFirstLastButtons","",3,"pageSizeOptions"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],[1,"status-badge",3,"ngClass"],["mat-header-cell",""],["mat-icon-button","","color","primary","matTooltip","View Details",3,"click"],["mat-icon-button","","color","accent","matTooltip","Edit",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","Complete Transfer",3,"click",4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Cancel Transfer",3,"click",4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","Edit",3,"click"],["mat-icon-button","","color","accent","matTooltip","Complete Transfer",3,"click"],["mat-icon-button","","color","warn","matTooltip","Cancel Transfer",3,"click"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click"],["mat-header-row",""],["mat-row",""],[1,"mat-row"],["colspan","6",1,"mat-cell"],[1,"no-data"]],template:function(o,r){o&1&&(n(0,"div",0)(1,"div",1)(2,"h1"),s(3,"Stock Transfers"),i(),n(4,"button",2),u("click",function(){return r.addStockTransfer()}),n(5,"mat-icon"),s(6,"add"),i(),s(7," New Stock Transfer "),i()(),_(8,$t,2,0,"div",3)(9,At,20,3,"mat-card",4)(10,se,24,5,"div",5),i()),o&2&&(l(8),d("ngIf",r.isLoading),l(),d("ngIf",!r.isLoading),l(),d("ngIf",!r.isLoading))},dependencies:[A,R,B,$,z,j,ut,rt,at,mt,st,ot,dt,ct,lt,ft,pt,_t,bt,M,vt,b,wt,Lt,yt,St,Ct,gt,kt,Y,K,X,tt,Z,it,et,nt,Nt,ht,Tt,J,Ft,Ot,Vt,It,Pt,Mt,xt,q,Q,W,U,G],styles:[".container[_ngcontent-%COMP%]{padding:20px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:500}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:200px;width:100%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background-color:#ffffffb3;z-index:999}.filter-card[_ngcontent-%COMP%]{margin-bottom:20px}.filter-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;align-items:center}.filter-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:200px}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin-bottom:20px}.table-container[_ngcontent-%COMP%]   .stock-transfer-table[_ngcontent-%COMP%]{width:100%}.table-container[_ngcontent-%COMP%]   .mat-column-referenceNumber[_ngcontent-%COMP%]{min-width:120px}.table-container[_ngcontent-%COMP%]   .mat-column-fromCostCenterName[_ngcontent-%COMP%], .table-container[_ngcontent-%COMP%]   .mat-column-toCostCenterName[_ngcontent-%COMP%]{min-width:150px}.table-container[_ngcontent-%COMP%]   .mat-column-transferDate[_ngcontent-%COMP%]{min-width:180px}.table-container[_ngcontent-%COMP%]   .mat-column-status[_ngcontent-%COMP%]{min-width:100px}.table-container[_ngcontent-%COMP%]   .mat-column-actions[_ngcontent-%COMP%]{min-width:150px;text-align:right}.status-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500;text-transform:uppercase}.status-draft[_ngcontent-%COMP%]{background-color:#e0e0e0;color:#616161}.status-pending[_ngcontent-%COMP%]{background-color:#bbdefb;color:#1976d2}.status-completed[_ngcontent-%COMP%]{background-color:#c8e6c9;color:#388e3c}.status-cancelled[_ngcontent-%COMP%]{background-color:#ffcdd2;color:#d32f2f}.no-data[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;color:#666}.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;margin-bottom:16px;opacity:.5}"]})}}return e})();export{Ue as StockTransferListComponent};
