export interface TransactionHeader {
  id: number;
  transactionNumber?: string;
  referenceNumber: string;
  transactionTypeId: number;
  transactionTypeName: string;
  transactionProcessId: number;
  transactionProcessName: string;
  stageTypeId?: number;
  stageTypeName?: string;
  sourceCostCenterId: number;
  sourceCostCenterName: string;
  destinationCostCenterId?: number;
  destinationCostCenterName?: string;
  supplierId?: number;
  supplierName?: string;
  transactionDate: Date;
  requiredDate?: Date;
  status: string;
  notes?: string;
  totalAmount?: number;
  taxAmount?: number;
  discountAmount?: number;
  netAmount?: number;
  createdById?: number;
  createdByName?: string;
  createdAt: Date;
  approvedById?: number;
  approvedByName?: string;
  approvedAt?: Date;
  completedById?: number;
  completedByName?: string;
  completedAt?: Date;
  details: TransactionDetail[];
}

export interface TransactionDetail {
  id: number;
  transactionHeaderId: number;
  productId: number;
  productCode: string;
  productName: string;
  batchId?: number;
  batchNumber?: string;
  expiryDate?: Date;
  unitId?: number;
  unitName?: string;
  quantity: number;
  unitPrice?: number;
  taxRate?: number;
  taxId?: number;
  taxName?: string;
  taxAmount?: number;
  discountPercent?: number;
  discountAmount?: number;
  totalAmount?: number;
  notes?: string;
  lineNumber: number;
}

// Create Transaction DTOs
export interface CreateProductRequest {
  sourceCostCenterId: number;
  transactionDate: Date;
  requiredDate?: Date;
  notes?: string;
  referenceNumber?: string;
  details: CreateTransactionDetail[];
}

export interface CreateProductOrder {
  supplierId: number;
  sourceCostCenterId: number;
  transactionDate: Date;
  requiredDate?: Date;
  referenceNumber?: string;
  notes?: string;
  details?: CreateTransactionDetail[];
}

export interface CreateReceiving {
  supplierId: number;
  sourceCostCenterId: number;
  transactionDate: Date;
  referenceNumber?: string;
  invoiceNumber?: string;
  deliveryNoteNumber?: string;
  notes?: string;
  details?: CreateTransactionDetail[];
}

export interface CreateTransactionDetail {
  productId: number;
  batchId?: number;
  unitId?: number;
  quantity: number;
  unitPrice?: number;
  taxId?: number;
  discountPercentage?: number;
  notes?: string;
}

// Transaction Status Types
export enum TransactionStatus {
  Draft = 'Draft',
  Submitted = 'Submitted',
  Approved = 'Approved',
  Rejected = 'Rejected',
  Completed = 'Completed',
  Cancelled = 'Cancelled'
}

// Transaction Stage Types
export enum TransactionStageType {
  Request = 1,
  Order = 2,
  Receiving = 3,
  Transfer = 4,
  Adjustment = 5,
  Production = 6,
  Return = 7,
  ReturnToSupplier = 8,
  CreditNote = 9
}
