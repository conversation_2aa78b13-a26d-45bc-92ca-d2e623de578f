using Microsoft.AspNetCore.Mvc;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;

public class ProductCostCenterLinksController : ApiControllerBase
{
    private readonly IProductCostCenterLinkService _productCostCenterLinkService;
    private readonly ILogger<ProductCostCenterLinksController> _logger;

    public ProductCostCenterLinksController(
        IProductCostCenterLinkService productCostCenterLinkService,
        ILogger<ProductCostCenterLinksController> logger)
    {
        _productCostCenterLinkService = productCostCenterLinkService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProductCostCenterLinkDto>>> GetAll()
    {
        try
        {
            var links = await _productCostCenterLinkService.GetAllProductCostCenterLinksAsync();
            return Ok(links);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving product cost center links");
            return StatusCode(500, "An error occurred while retrieving product cost center links");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductCostCenterLinkDto>> GetById(int id)
    {
        try
        {
            var link = await _productCostCenterLinkService.GetProductCostCenterLinkByIdAsync(id);
            if (link == null)
                return NotFound();

            return Ok(link);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving product cost center link with ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the product cost center link. Please try again later.");
        }
    }

    [HttpGet("product/{productId}")]
    public async Task<ActionResult<IEnumerable<ProductCostCenterLinkDto>>> GetByProductId(int productId)
    {
        try
        {
            var links = await _productCostCenterLinkService.GetProductCostCenterLinksByProductIdAsync(productId);
            return Ok(links);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving product cost center links for product ID {ProductId}", productId);
            return StatusCode(500, "An error occurred while retrieving product cost center links. Please try again later.");
        }
    }

    [HttpGet("costcenter/{costCenterId}")]
    public async Task<ActionResult<IEnumerable<ProductCostCenterLinkDto>>> GetByCostCenterId(int costCenterId)
    {
        try
        {
            var links = await _productCostCenterLinkService.GetProductCostCenterLinksByCostCenterIdAsync(costCenterId);
            return Ok(links);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving product cost center links for cost center ID {CostCenterId}", costCenterId);
            return StatusCode(500, "An error occurred while retrieving product cost center links. Please try again later.");
        }
    }

    [HttpGet("product/{productId}/costcenter/{costCenterId}")]
    public async Task<ActionResult<ProductCostCenterLinkDto>> GetByProductAndCostCenter(int productId, int costCenterId)
    {
        try
        {
            var link = await _productCostCenterLinkService.GetProductCostCenterLinkAsync(productId, costCenterId);
            if (link == null)
                return NotFound();

            return Ok(link);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving product cost center link for product ID {ProductId} and cost center ID {CostCenterId}", productId, costCenterId);
            return StatusCode(500, "An error occurred while retrieving the product cost center link. Please try again later.");
        }
    }

    [HttpPost]
    public async Task<ActionResult<ProductCostCenterLinkDto>> Create(CreateProductCostCenterLinkDto createProductCostCenterLinkDto)
    {
        try
        {
            var link = await _productCostCenterLinkService.CreateProductCostCenterLinkAsync(createProductCostCenterLinkDto);
            return CreatedAtAction(nameof(GetById), new { id = link.Id }, link);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating product cost center link");
            return StatusCode(500, "An error occurred while creating the product cost center link. Please try again later.");
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, UpdateProductCostCenterLinkDto updateProductCostCenterLinkDto)
    {
        if (id != updateProductCostCenterLinkDto.Id)
            return BadRequest("ID mismatch");

        try
        {
            await _productCostCenterLinkService.UpdateProductCostCenterLinkAsync(updateProductCostCenterLinkDto);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating product cost center link with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the product cost center link. Please try again later.");
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            await _productCostCenterLinkService.DeleteProductCostCenterLinkAsync(id);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting product cost center link with ID {Id}", id);
            return StatusCode(500, "An error occurred while deleting the product cost center link. Please try again later.");
        }
    }

    [HttpGet("cost-centers-with-status/{productId}")]
    public async Task<ActionResult<IEnumerable<CostCenterWithLinkDto>>> GetCostCentersWithLinkStatus(int productId, [FromQuery] string? searchTerm = null)
    {
        try
        {
            var result = await _productCostCenterLinkService.GetCostCentersWithLinkStatusAsync(productId, searchTerm);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cost centers with link status for product ID {ProductId}", productId);
            return StatusCode(500, "An error occurred while retrieving cost centers with link status. Please try again later.");
        }
    }

    [HttpPost("toggle-link")]
    public async Task<IActionResult> ToggleLink([FromBody] ToggleLinkRequest request)
    {
        try
        {
            var result = await _productCostCenterLinkService.ToggleLinkAsync(
                request.ProductId,
                request.CostCenterId,
                request.IsLinked,
                request.MinStock,
                request.MaxStock,
                request.ReorderPoint);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling link for product ID {ProductId} and cost center ID {CostCenterId}", request.ProductId, request.CostCenterId);
            return StatusCode(500, "An error occurred while toggling the link. Please try again later.");
        }
    }

    public class ToggleLinkRequest
    {
        public int ProductId { get; set; }
        public int CostCenterId { get; set; }
        public bool IsLinked { get; set; }
        public decimal? MinStock { get; set; }
        public decimal? MaxStock { get; set; }
        public decimal? ReorderPoint { get; set; }
    }
}
