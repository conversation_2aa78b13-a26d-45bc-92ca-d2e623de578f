import './polyfills.server.mjs';
import{a as u}from"./chunk-2IOWMPES.mjs";import{a as m}from"./chunk-I44XXN6Y.mjs";import{W as t,Z as o,ca as n,l as e,p as h}from"./chunk-FFIKRRNO.mjs";var d=(()=>{class i{constructor(s,r){this.apiService=s,this.authService=r,this.path="permissions",this.userPermissionsPath="user-permissions",this.cachedUserPermissions={},this.cachedRolePermissions={},this.cachedFormPermissions={},this.currentUserPermissions=null,this.authService.logout$.subscribe(()=>{this.clearCache()})}getAllPermissions(){return this.apiService.get(this.path)}getRolePermissions(s){return this.cachedRolePermissions[s]?e(this.cachedRolePermissions[s]):this.apiService.get(`${this.path}/roles/${s}`)}getUserPermissions(s){return this.cachedUserPermissions[s]?e(this.cachedUserPermissions[s]):this.apiService.get(`${this.path}/users/${s}`)}getFormPermissions(s){if(this.cachedFormPermissions[s])return e(this.cachedFormPermissions[s]);let r=this.authService.getCurrentUser();return r?this.apiService.get(`${this.path}/forms/${s}/users/${r.id}`):e({formName:s,actions:[]})}getCurrentUserPermissions(){return this.currentUserPermissions?e(this.currentUserPermissions):this.apiService.get(`${this.userPermissionsPath}/current`).pipe(t(s=>this.currentUserPermissions=s))}checkPermission(s){return this.apiService.get(`${this.userPermissionsPath}/check/${s}`)}clearCache(){this.currentUserPermissions=null,this.cachedUserPermissions={},this.cachedRolePermissions={},this.cachedFormPermissions={}}hasPermission(s){if(this.authService.hasRole("Admin"))return!0;if(!this.authService.getCurrentUser())return!1;if(this.currentUserPermissions)switch(s){case"transactions.view":return this.currentUserPermissions.canViewTransactions;case"transactions.create":return this.currentUserPermissions.canCreateTransactions;case"transactions.edit":return this.currentUserPermissions.canEditTransactions;case"transactions.delete":return this.currentUserPermissions.canDeleteTransactions;case"transactions.approve":return this.currentUserPermissions.canApproveTransactions;case"receiving.full":return this.currentUserPermissions.hasFullReceivePermission}return s.startsWith("product.")||s.startsWith("inventory.")||s.startsWith("transaction.")?!0:s.startsWith("user.")&&!this.authService.hasRole("Admin")?!1:!!s.startsWith("config.")}hasPermissionAsync(s){return this.authService.hasRole("Admin")?e(!0):this.authService.getCurrentUser()?this.currentUserPermissions?e(this.hasPermission(s)):this.getCurrentUserPermissions().pipe(t(()=>{}),h(()=>this.hasPermission(s))):e(!1)}canPerformAction(s,r){if(this.authService.hasRole("Admin"))return!0;let c=this.cachedFormPermissions[s];if(!c)return!1;let a=c.actions.find(P=>P.name===r);return a?a.allowed:!1}static{this.\u0275fac=function(r){return new(r||i)(n(m),n(u))}}static{this.\u0275prov=o({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();export{d as a};
