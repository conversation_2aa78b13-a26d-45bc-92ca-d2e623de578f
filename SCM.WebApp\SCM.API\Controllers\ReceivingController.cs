using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;

namespace SCM.API.Controllers;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

[Authorize]
public class ReceivingController : ApiControllerBase
{
    private readonly ITransactionService _transactionService;
    private readonly ILogger<ReceivingController> _logger;
    private readonly ApplicationDbContext _dbContext;

    public ReceivingController(ITransactionService transactionService, ILogger<ReceivingController> logger, ApplicationDbContext dbContext)
    {
        _transactionService = transactionService;
        _logger = logger;
        _dbContext = dbContext;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetAll()
    {
        try
        {
            // Get all transactions that are receiving (stage type = Receiving)
            var stageTypes = await _transactionService.GetAllTransactionStageTypesAsync();
            var receivingStageType = stageTypes.FirstOrDefault(st => st.Name == "Receiving");

            if (receivingStageType == null)
                return Ok(new List<TransactionHeaderDto>());

            var receivings = await _transactionService.GetTransactionsByStageTypeIdAsync(receivingStageType.StageTypeId);
            return Ok(receivings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all receivings");
            return StatusCode(500, "An error occurred while retrieving receivings");
        }
    }

    [HttpGet("available-for-credit-note")]
    public async Task<ActionResult<IEnumerable<TransactionHeaderDto>>> GetAvailableForCreditNote()
    {
        try
        {
            var transactions = await _transactionService.GetReceivingTransactionsAvailableForCreditNoteAsync();
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting receiving transactions available for credit note");
            return StatusCode(500, "An error occurred while retrieving receiving transactions");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<TransactionHeaderDto>> GetById(int id)
    {
        try
        {
            var receiving = await _transactionService.GetTransactionByIdAsync(id);
            if (receiving == null)
                return NotFound();

            return Ok(receiving);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the receiving");
        }
    }

    [HttpPost]
    public async Task<ActionResult<TransactionHeaderDto>> Create(CreateReceivingDto createReceivingDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateReceivingAsync(createReceivingDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating receiving");
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating receiving");
            return StatusCode(500, "An error occurred while creating the receiving");
        }
    }

    [HttpPost("from-order/{productOrderId}")]
    public async Task<ActionResult<TransactionHeaderDto>> CreateFromOrder(int productOrderId, CreateReceivingDto createReceivingDto)
    {
        try
        {
            int userId = GetCurrentUserIdRequired();
            var transaction = await _transactionService.CreateReceivingFromOrderAsync(productOrderId, createReceivingDto, userId);
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when creating receiving from order {ProductOrderId}", productOrderId);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating receiving from order with ID {ProductOrderId}", productOrderId);
            return StatusCode(500, "An error occurred while creating the receiving");
        }
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, UpdateReceivingDto updateReceivingDto)
    {
        try
        {
            updateReceivingDto.Id = id;
            await _transactionService.UpdateReceivingAsync(id, updateReceivingDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the receiving");
        }
    }

    [HttpPut("{id}/submit")]
    public async Task<ActionResult> Submit(int id, [FromBody] object data)
    {
        try
        {
            string? notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString() ?? "{}");
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            await _transactionService.SubmitReceivingAsync(id, notes);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while submitting the receiving");
        }
    }

    [HttpPut("{id}/approve")]
    public async Task<ActionResult> Approve(int id, [FromBody] object data)
    {
        try
        {
            string? notes = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString() ?? "{}");
                if (jsonData.RootElement.TryGetProperty("notes", out var notesElement))
                {
                    notes = notesElement.GetString();
                }
            }

            int userId = GetCurrentUserIdRequired();
            await _transactionService.ApproveReceivingAsync(id, userId, notes);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt when approving receiving {Id}", id);
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while approving the receiving");
        }
    }

    [HttpPut("{id}/reject")]
    public async Task<ActionResult> Reject(int id, [FromBody] object data)
    {
        try
        {
            string reason = "";
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString() ?? "{}");
                if (jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
                {
                    reason = reasonElement.GetString() ?? "";
                }
            }

            await _transactionService.RejectReceivingAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while rejecting the receiving");
        }
    }

    [HttpPut("{id}/complete")]
    public async Task<ActionResult> Complete(int id)
    {
        _logger.LogInformation("=== COMPLETION DEBUG: Starting completion for receiving {Id} ===", id);
        try
        {
            // Get the transaction and update stock directly
            var transaction = await _dbContext.TransactionHeaders
                .Include(t => t.Details)
                .FirstOrDefaultAsync(t => t.Id == id);

            _logger.LogInformation("COMPLETION DEBUG: Transaction found: {Found}, Status: {Status}, Details count: {Count}",
                transaction != null, transaction?.Status, transaction?.Details?.Count ?? 0);

            if (transaction == null)
                return NotFound("Receiving not found");

            if (transaction.Status == "Completed")
                return BadRequest("Receiving is already completed");

            // Force load details if not loaded
            if (!transaction.Details.Any())
            {
                _logger.LogInformation("COMPLETION DEBUG: No details loaded, forcing load...");
                await _dbContext.Entry(transaction)
                    .Collection(t => t.Details)
                    .LoadAsync();
                _logger.LogInformation("COMPLETION DEBUG: After force load, details count: {Count}", transaction.Details.Count);
            }

            _logger.LogInformation("COMPLETION DEBUG: Processing {Count} details", transaction.Details.Count);

            // Update stock for each detail
            foreach (var detail in transaction.Details)
            {
                _logger.LogInformation("COMPLETION DEBUG: Processing detail - ProductId: {ProductId}, Quantity: {Quantity}, UnitPrice: {UnitPrice}",
                    detail.ProductId, detail.Quantity, detail.UnitPrice);

                if (detail.Quantity > 0)
                {
                    _logger.LogInformation("COMPLETION DEBUG: Updating stock for ProductId {ProductId}, CostCenter {CostCenter}, Quantity {Quantity}",
                        detail.ProductId, transaction.SourceCostCenterId.Value, detail.Quantity);

                    // Get unit conversion factor for base quantity calculation
                    var unit = await _dbContext.Units.FirstOrDefaultAsync(u => u.Id == detail.UnitId);

                    // Try to get BaseConversionFactor, fall back to ConversionFactor, or use 1.0 as last resort
                    decimal baseConversionFactor = 1.0m;
                    if (unit?.BaseConversionFactor > 0)
                    {
                        baseConversionFactor = unit.BaseConversionFactor;
                    }
                    else if (unit?.ConversionFactor > 0)
                    {
                        baseConversionFactor = unit.ConversionFactor.Value;
                    }

                    var baseQuantity = detail.Quantity * baseConversionFactor;
                    var totalCost = detail.LineTotal; // Use LineTotal instead of calculating

                    _logger.LogInformation("COMPLETION DEBUG: Unit details - UnitId: {UnitId}, UnitName: {UnitName}, BaseConversionFactor: {Factor}, ConversionFactor: {ConversionFactor}",
                        detail.UnitId, unit?.Name ?? "NULL", unit?.BaseConversionFactor, unit?.ConversionFactor);
                    _logger.LogInformation("COMPLETION DEBUG: Calculations - Quantity: {Quantity}, BaseQuantity: {BaseQuantity}, UnitPrice: {UnitPrice}, LineTotal: {LineTotal}, TotalCost: {TotalCost}",
                        detail.Quantity, baseQuantity, detail.UnitPrice, detail.LineTotal, totalCost);

                    // Use MERGE statement to handle both insert and update in one operation
                    var mergeSql = @"
                        MERGE StockOnHand AS target
                        USING (SELECT @ProductId AS ProductId, @CostCenterId AS CostCenterId) AS source
                        ON target.ProductId = source.ProductId AND target.CostCenterId = source.CostCenterId
                        WHEN MATCHED THEN
                            UPDATE SET
                                Quantity = target.Quantity + @AddQuantity,
                                BaseQuantity = ISNULL(target.BaseQuantity, 0) + @BaseQuantity,
                                AverageCost = (ISNULL(target.Quantity, 0) * ISNULL(target.AverageCost, 0) + @TotalCost) / (ISNULL(target.Quantity, 0) + @AddQuantity),
                                CostPrice = @NewCostPrice,
                                LastUpdated = @LastUpdated
                        WHEN NOT MATCHED THEN
                            INSERT (ProductId, CostCenterId, UnitId, Quantity, BaseQuantity, AverageCost, CostPrice, LastUpdated)
                            VALUES (@ProductId, @CostCenterId, @UnitId, @AddQuantity, @BaseQuantity, @NewCostPrice, @NewCostPrice, @LastUpdated);";

                    var rowsAffected = await _dbContext.Database.ExecuteSqlRawAsync(mergeSql,
                        new Microsoft.Data.SqlClient.SqlParameter("@ProductId", detail.ProductId),
                        new Microsoft.Data.SqlClient.SqlParameter("@CostCenterId", transaction.SourceCostCenterId.Value),
                        new Microsoft.Data.SqlClient.SqlParameter("@UnitId", detail.UnitId),
                        new Microsoft.Data.SqlClient.SqlParameter("@AddQuantity", detail.Quantity),
                        new Microsoft.Data.SqlClient.SqlParameter("@BaseQuantity", baseQuantity),
                        new Microsoft.Data.SqlClient.SqlParameter("@TotalCost", totalCost),
                        new Microsoft.Data.SqlClient.SqlParameter("@NewCostPrice", detail.UnitPrice),
                        new Microsoft.Data.SqlClient.SqlParameter("@LastUpdated", DateTime.UtcNow));

                    _logger.LogInformation("COMPLETION DEBUG: Stock updated, rows affected: {RowsAffected}", rowsAffected);

                    // Update product cost price (last received price) and calculate average cost
                    var updateProductSql = @"
                        UPDATE Product
                        SET CostPrice = @LastReceivedPrice,
                            AverageCost = (
                                SELECT SUM(Quantity * ISNULL(AverageCost, 0)) / NULLIF(SUM(Quantity), 0)
                                FROM StockOnHand
                                WHERE ProductId = @ProductId AND Quantity > 0
                            )
                        WHERE ProductId = @ProductId";

                    var productRowsAffected = await _dbContext.Database.ExecuteSqlRawAsync(updateProductSql,
                        new Microsoft.Data.SqlClient.SqlParameter("@ProductId", detail.ProductId),
                        new Microsoft.Data.SqlClient.SqlParameter("@LastReceivedPrice", detail.UnitPrice));

                    _logger.LogInformation("COMPLETION DEBUG: Product cost price (last received: {LastPrice}) and average cost updated, rows affected: {RowsAffected}",
                        detail.UnitPrice, productRowsAffected);
                }
            }

            // Update transaction status
            transaction.Status = "Completed";
            transaction.UpdatedAt = DateTime.UtcNow;
            _dbContext.TransactionHeaders.Update(transaction);

            _logger.LogInformation("COMPLETION DEBUG: Saving all changes to database...");
            await _dbContext.SaveChangesAsync();
            _logger.LogInformation("COMPLETION DEBUG: All changes saved successfully");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while completing the receiving");
        }
    }

    [HttpPut("{id}/cancel")]
    public async Task<ActionResult> CancelReceiving(int id, [FromBody] object data)
    {
        try
        {
            string? reason = null;
            if (data != null)
            {
                var jsonData = System.Text.Json.JsonDocument.Parse(data.ToString() ?? "{}");
                if (jsonData.RootElement.TryGetProperty("reason", out var reasonElement))
                {
                    reason = reasonElement.GetString();
                }
            }

            await _transactionService.CancelReceivingAsync(id, reason);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling receiving with ID {Id}", id);
            return StatusCode(500, "An error occurred while cancelling the receiving");
        }
    }

    [HttpGet("{id}/details")]
    public async Task<ActionResult<IEnumerable<TransactionDetailDto>>> GetDetails(int id)
    {
        try
        {
            var transaction = await _transactionService.GetTransactionByIdAsync(id);
            if (transaction == null)
                return NotFound();

            return Ok(transaction.Details ?? new List<TransactionDetailDto>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting receiving details for ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving receiving details");
        }
    }

    [HttpPut("{id}/details/{detailId}")]
    public async Task<ActionResult> UpdateDetail(int id, int detailId, UpdateReceivingDetailDto updateDetailDto)
    {
        try
        {
            await _transactionService.UpdateReceivingDetailAsync(id, detailId, updateDetailDto);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating receiving detail with ID {DetailId}", detailId);
            return StatusCode(500, "An error occurred while updating the detail");
        }
    }

}
