using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Domain.Interfaces.Repositories;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class ProductCostCenterLinkService : IProductCostCenterLinkService
{
    private readonly IRepository<ProductCostCenterLink> _productCostCenterLinkRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;

    public ProductCostCenterLinkService(
        IRepository<ProductCostCenterLink> productCostCenterLinkRepository,
        ApplicationDbContext dbContext,
        IMapper mapper)
    {
        _productCostCenterLinkRepository = productCostCenterLinkRepository;
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<IEnumerable<ProductCostCenterLinkDto>> GetAllProductCostCenterLinksAsync()
    {
        var links = await _dbContext.ProductCostCenterLinks
            .Include(pcl => pcl.Product)
            .Include(pcl => pcl.CostCenter)
            .Where(pcl => pcl.IsActive)
            .OrderBy(pcl => pcl.Product.Name)
            .ThenBy(pcl => pcl.CostCenter.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ProductCostCenterLinkDto>>(links);
    }

    public async Task<ProductCostCenterLinkDto?> GetProductCostCenterLinkByIdAsync(int id)
    {
        var link = await _dbContext.ProductCostCenterLinks
            .Include(pcl => pcl.Product)
            .Include(pcl => pcl.CostCenter)
            .FirstOrDefaultAsync(pcl => pcl.Id == id && pcl.IsActive);

        return link != null ? _mapper.Map<ProductCostCenterLinkDto>(link) : null;
    }

    public async Task<ProductCostCenterLinkDto?> GetProductCostCenterLinkAsync(int productId, int costCenterId)
    {
        var link = await _dbContext.ProductCostCenterLinks
            .Include(pcl => pcl.Product)
            .Include(pcl => pcl.CostCenter)
            .FirstOrDefaultAsync(pcl => pcl.ProductId == productId && pcl.CostCenterId == costCenterId && pcl.IsActive);

        return link != null ? _mapper.Map<ProductCostCenterLinkDto>(link) : null;
    }

    public async Task<IEnumerable<ProductCostCenterLinkDto>> GetProductCostCenterLinksByProductIdAsync(int productId)
    {
        var links = await _dbContext.ProductCostCenterLinks
            .Include(pcl => pcl.Product)
            .Include(pcl => pcl.CostCenter)
            .Where(pcl => pcl.ProductId == productId && pcl.IsActive)
            .OrderBy(pcl => pcl.CostCenter.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ProductCostCenterLinkDto>>(links);
    }

    public async Task<IEnumerable<ProductCostCenterLinkDto>> GetProductCostCenterLinksByCostCenterIdAsync(int costCenterId)
    {
        var links = await _dbContext.ProductCostCenterLinks
            .Include(pcl => pcl.Product)
            .Include(pcl => pcl.CostCenter)
            .Where(pcl => pcl.CostCenterId == costCenterId && pcl.IsActive)
            .OrderBy(pcl => pcl.Product.Name)
            .ToListAsync();

        return _mapper.Map<IEnumerable<ProductCostCenterLinkDto>>(links);
    }

    public async Task<ProductCostCenterLinkDto> CreateProductCostCenterLinkAsync(CreateProductCostCenterLinkDto createProductCostCenterLinkDto)
    {
        // Check if link already exists
        var existingLink = await _dbContext.ProductCostCenterLinks
            .FirstOrDefaultAsync(pcl => pcl.ProductId == createProductCostCenterLinkDto.ProductId 
                                     && pcl.CostCenterId == createProductCostCenterLinkDto.CostCenterId);

        if (existingLink != null)
        {
            if (existingLink.IsActive)
            {
                throw new InvalidOperationException("A link between this product and cost center already exists.");
            }
            else
            {
                // Reactivate the existing link
                existingLink.IsActive = true;
                existingLink.MinStock = createProductCostCenterLinkDto.MinStock;
                existingLink.MaxStock = createProductCostCenterLinkDto.MaxStock;
                existingLink.ReorderPoint = createProductCostCenterLinkDto.ReorderPoint;
                existingLink.AutoReorder = createProductCostCenterLinkDto.AutoReorder;
                existingLink.UpdatedAt = DateTime.UtcNow;

                await _dbContext.SaveChangesAsync();

                // Reload with navigation properties
                var reactivatedLink = await _dbContext.ProductCostCenterLinks
                    .Include(pcl => pcl.Product)
                    .Include(pcl => pcl.CostCenter)
                    .FirstOrDefaultAsync(pcl => pcl.Id == existingLink.Id);

                return _mapper.Map<ProductCostCenterLinkDto>(reactivatedLink!);
            }
        }

        // Validate that product and cost center exist
        var product = await _dbContext.Products.FindAsync(createProductCostCenterLinkDto.ProductId);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {createProductCostCenterLinkDto.ProductId} not found.");

        var costCenter = await _dbContext.CostCenters.FindAsync(createProductCostCenterLinkDto.CostCenterId);
        if (costCenter == null)
            throw new KeyNotFoundException($"Cost center with ID {createProductCostCenterLinkDto.CostCenterId} not found.");

        var link = _mapper.Map<ProductCostCenterLink>(createProductCostCenterLinkDto);
        link.IsActive = true;
        link.CreatedAt = DateTime.UtcNow;

        await _productCostCenterLinkRepository.AddAsync(link);

        // Reload with navigation properties
        var createdLink = await _dbContext.ProductCostCenterLinks
            .Include(pcl => pcl.Product)
            .Include(pcl => pcl.CostCenter)
            .FirstOrDefaultAsync(pcl => pcl.Id == link.Id);

        return _mapper.Map<ProductCostCenterLinkDto>(createdLink!);
    }

    public async Task UpdateProductCostCenterLinkAsync(UpdateProductCostCenterLinkDto updateProductCostCenterLinkDto)
    {
        var link = await _productCostCenterLinkRepository.GetByIdAsync(updateProductCostCenterLinkDto.Id);
        if (link == null)
            throw new KeyNotFoundException($"Product cost center link with ID {updateProductCostCenterLinkDto.Id} not found.");

        // Check if trying to change product/cost center combination to one that already exists
        if (link.ProductId != updateProductCostCenterLinkDto.ProductId || link.CostCenterId != updateProductCostCenterLinkDto.CostCenterId)
        {
            var existingLink = await _dbContext.ProductCostCenterLinks
                .FirstOrDefaultAsync(pcl => pcl.ProductId == updateProductCostCenterLinkDto.ProductId 
                                         && pcl.CostCenterId == updateProductCostCenterLinkDto.CostCenterId
                                         && pcl.Id != updateProductCostCenterLinkDto.Id
                                         && pcl.IsActive);

            if (existingLink != null)
                throw new InvalidOperationException("A link between this product and cost center already exists.");
        }

        _mapper.Map(updateProductCostCenterLinkDto, link);
        link.UpdatedAt = DateTime.UtcNow;

        await _productCostCenterLinkRepository.UpdateAsync(link);
    }

    public async Task DeleteProductCostCenterLinkAsync(int id)
    {
        var link = await _productCostCenterLinkRepository.GetByIdAsync(id);
        if (link == null)
            throw new KeyNotFoundException($"Product cost center link with ID {id} not found.");

        // Soft delete by setting IsActive to false
        link.IsActive = false;
        link.UpdatedAt = DateTime.UtcNow;

        await _productCostCenterLinkRepository.UpdateAsync(link);
    }

    public async Task<IEnumerable<CostCenterWithLinkDto>> GetCostCentersWithLinkStatusAsync(int productId, string? searchTerm = null)
    {
        var query = from cc in _dbContext.CostCenters
                    join link in _dbContext.ProductCostCenterLinks.Where(l => l.ProductId == productId && l.IsActive)
                        on cc.Id equals link.CostCenterId into linkGroup
                    from link in linkGroup.DefaultIfEmpty()
                    join stock in _dbContext.StockOnHand.Where(s => s.ProductId == productId)
                        on cc.Id equals stock.CostCenterId into stockGroup
                    from stock in stockGroup.DefaultIfEmpty()
                    select new CostCenterWithLinkDto
                    {
                        CostCenterId = cc.Id,
                        CostCenterName = cc.Name,
                        IsLinked = link != null,
                        LinkId = link != null ? link.Id : null,
                        MinStock = link != null ? link.MinStock : null,
                        MaxStock = link != null ? link.MaxStock : null,
                        ReorderPoint = link != null ? link.ReorderPoint : null,
                        AutoReorder = link != null ? link.AutoReorder : false,
                        StockOnHand = stock != null ? stock.Quantity : 0
                    };

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(x => x.CostCenterName.Contains(searchTerm));
        }

        return await query.OrderBy(x => x.CostCenterName).ToListAsync();
    }

    public async Task<bool> ToggleLinkAsync(int productId, int costCenterId, bool isLinked, decimal? minStock = null, decimal? maxStock = null, decimal? reorderPoint = null)
    {
        var existingLink = await _dbContext.ProductCostCenterLinks
            .FirstOrDefaultAsync(l => l.ProductId == productId && l.CostCenterId == costCenterId);

        if (isLinked)
        {
            if (existingLink == null)
            {
                // Create new link
                var createDto = new CreateProductCostCenterLinkDto
                {
                    ProductId = productId,
                    CostCenterId = costCenterId,
                    MinStock = minStock,
                    MaxStock = maxStock,
                    ReorderPoint = reorderPoint,
                    AutoReorder = false
                };
                await CreateProductCostCenterLinkAsync(createDto);
            }
            else
            {
                // Update existing link
                var updateDto = new UpdateProductCostCenterLinkDto
                {
                    Id = existingLink.Id,
                    ProductId = productId,
                    CostCenterId = costCenterId,
                    MinStock = minStock,
                    MaxStock = maxStock,
                    ReorderPoint = reorderPoint,
                    AutoReorder = existingLink.AutoReorder
                };
                await UpdateProductCostCenterLinkAsync(updateDto);
            }
        }
        else
        {
            if (existingLink != null)
            {
                // Soft delete link
                await DeleteProductCostCenterLinkAsync(existingLink.Id);
            }
        }

        return true;
    }
}
