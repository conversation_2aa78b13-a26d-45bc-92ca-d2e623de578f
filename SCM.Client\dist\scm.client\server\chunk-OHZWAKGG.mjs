import './polyfills.server.mjs';
import{a as Ee}from"./chunk-VBJ5VJVU.mjs";import{a as be}from"./chunk-4I5ZMQSL.mjs";import{b as ce,c as ue,d as fe,e as he}from"./chunk-4JYGXCOT.mjs";import{c as Ce,h as De}from"./chunk-3LTRCH3K.mjs";import"./chunk-CHXIZKOG.mjs";import{b as pe}from"./chunk-J2FNDWGV.mjs";import{b as _e,c as ge}from"./chunk-GBVBP5JO.mjs";import{a as me,b as le,h as se,i as de}from"./chunk-KCBYLGU4.mjs";import{b as k,d as v,f as O,g as A,k as L,n as B,q as N,u as H,w as R}from"./chunk-R2YOBFRV.mjs";import"./chunk-2IOWMPES.mjs";import{a as W,b as X,c as Y,d as Z,e as ee,f as te,g as ne,h as ie,i as re,j as ae,l as oe}from"./chunk-CUT3Q574.mjs";import{a as xe,b as Se}from"./chunk-S2DPCQFA.mjs";import{a as ve,b as Me}from"./chunk-4QI52JLP.mjs";import{a as U,b as z,c as J,f as K,h as Q}from"./chunk-PWFZPCXU.mjs";import"./chunk-I44XXN6Y.mjs";import"./chunk-A3ICGYXF.mjs";import{_ as V,aa as j,ba as $,ca as G,da as q}from"./chunk-N6IFEGQ2.mjs";import"./chunk-46VHTMVE.mjs";import{Ec as F,Fb as r,Gb as g,Gc as P,Hb as S,Ka as m,La as c,Lc as T,Qb as w,ab as d,cb as s,ha as y,lb as n,mb as i,nb as u,oa as M,ob as f,pa as x,pb as h,rb as I,ub as _,wb as D}from"./chunk-FFIKRRNO.mjs";import{a as b}from"./chunk-VVCT4QZE.mjs";function ye(e,o){e&1&&(n(0,"mat-error"),r(1," Department name is required "),i())}function Ie(e,o){e&1&&(n(0,"mat-error"),r(1," Department name cannot exceed 100 characters "),i())}function we(e,o){e&1&&(n(0,"mat-error"),r(1," Description cannot exceed 500 characters "),i())}function Fe(e,o){e&1&&(n(0,"div",27),u(1,"mat-spinner",28),i())}function Pe(e,o){e&1&&(n(0,"th",29),r(1,"ID"),i())}function Te(e,o){if(e&1&&(n(0,"td",30),r(1),i()),e&2){let t=o.$implicit;m(),g(t.id)}}function ke(e,o){e&1&&(n(0,"th",29),r(1,"Name"),i())}function Oe(e,o){if(e&1&&(n(0,"td",30),r(1),i()),e&2){let t=o.$implicit;m(),g(t.name)}}function Ae(e,o){e&1&&(n(0,"th",31),r(1,"Description"),i())}function Le(e,o){if(e&1&&(n(0,"td",30),r(1),i()),e&2){let t=o.$implicit;m(),g(t.description)}}function Be(e,o){e&1&&(n(0,"th",29),r(1,"Status"),i())}function Ne(e,o){if(e&1&&(n(0,"td",30)(1,"span",32),r(2),i()()),e&2){let t=o.$implicit;m(),s("ngClass",t.isActive?"status-active":"status-inactive"),m(),S(" ",t.isActive?"Active":"Inactive"," ")}}function He(e,o){e&1&&(n(0,"th",31),r(1,"Actions"),i())}function Re(e,o){if(e&1){let t=I();n(0,"td",30)(1,"button",33),_("click",function(){let a=M(t).$implicit,p=D();return x(p.editDepartment(a))}),n(2,"mat-icon"),r(3,"edit"),i()(),n(4,"button",34),_("click",function(){let a=M(t).$implicit,p=D();return x(p.deleteDepartment(a.id))}),n(5,"mat-icon"),r(6,"delete"),i()()()}if(e&2){let t=D();m(),s("disabled",!t.canEdit()),m(3),s("disabled",!t.canDelete())}}function Ve(e,o){e&1&&u(0,"tr",35)}function je(e,o){e&1&&u(0,"tr",36)}function $e(e,o){e&1&&(n(0,"div",37),r(1," No departments found. Please add a department. "),i())}var Ct=(()=>{class e{constructor(t,l,a,p,C){this.fb=t,this.departmentService=l,this.permissionsService=a,this.snackBar=p,this.dialog=C,this.departments=[],this.isLoading=!1,this.isEditing=!1,this.currentDepartmentId=null,this.displayedColumns=["id","name","description","isActive","actions"]}ngOnInit(){this.initForm(),this.loadDepartments()}initForm(){this.departmentForm=this.fb.group({name:["",[v.required,v.maxLength(100)]],description:["",v.maxLength(500)],isActive:[!0]})}loadDepartments(){this.isLoading=!0,this.departmentService.getAll().subscribe({next:t=>{this.departments=t,this.isLoading=!1},error:t=>{console.error("Error loading departments",t),this.snackBar.open("Error loading departments. Please try again.","Close",{duration:3e3}),this.isLoading=!1}})}onSubmit(){if(!this.departmentForm.invalid)if(this.isLoading=!0,this.isEditing&&this.currentDepartmentId){let t=b({id:this.currentDepartmentId},this.departmentForm.value);this.departmentService.update(this.currentDepartmentId,t).subscribe({next:()=>{this.snackBar.open("Department updated successfully","Close",{duration:3e3}),this.resetForm(),this.loadDepartments()},error:l=>{console.error("Error updating department",l),this.snackBar.open("Error updating department. Please try again.","Close",{duration:3e3}),this.isLoading=!1}})}else{let t=this.departmentForm.value;this.departmentService.create(t).subscribe({next:()=>{this.snackBar.open("Department created successfully","Close",{duration:3e3}),this.resetForm(),this.loadDepartments()},error:l=>{console.error("Error creating department",l),this.snackBar.open("Error creating department. Please try again.","Close",{duration:3e3}),this.isLoading=!1}})}}editDepartment(t){this.isEditing=!0,this.currentDepartmentId=t.id,this.departmentForm.patchValue({name:t.name,description:t.description||"",isActive:t.isActive})}deleteDepartment(t){confirm("Are you sure you want to delete this department?")&&(this.isLoading=!0,this.departmentService.delete(t).subscribe({next:()=>{this.snackBar.open("Department deleted successfully","Close",{duration:3e3}),this.loadDepartments()},error:l=>{console.error("Error deleting department",l),this.snackBar.open("Error deleting department. Please try again.","Close",{duration:3e3}),this.isLoading=!1}}))}resetForm(){this.departmentForm.reset({name:"",description:"",isActive:!0}),this.isEditing=!1,this.currentDepartmentId=null,this.isLoading=!1}canEdit(){return this.permissionsService.hasPermission("config.department.edit")}canDelete(){return this.permissionsService.hasPermission("config.department.delete")}static{this.\u0275fac=function(l){return new(l||e)(c(H),c(be),c(Ee),c(ve),c(Ce))}}static{this.\u0275cmp=y({type:e,selectors:[["app-departments"]],standalone:!0,features:[w],decls:56,vars:12,consts:[[1,"departments-container"],[1,"departments-content"],[1,"form-card"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","name","placeholder","Enter department name"],[4,"ngIf"],["matInput","","formControlName","description","placeholder","Enter description","rows","3"],[1,"form-field-checkbox"],["formControlName","isActive"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["mat-button","","type","button",3,"click"],[1,"table-card"],["class","spinner-container",4,"ngIf"],["mat-table","","matSort","",1,"full-width",3,"dataSource"],["matColumnDef","id"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","name"],["matColumnDef","description"],["mat-header-cell","",4,"matHeaderCellDef"],["matColumnDef","isActive"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["class","no-data-message",4,"ngIf"],[1,"spinner-container"],["diameter","40"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],["mat-header-cell",""],[3,"ngClass"],["mat-icon-button","","color","primary","matTooltip","Edit",3,"click","disabled"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click","disabled"],["mat-header-row",""],["mat-row",""],[1,"no-data-message"]],template:function(l,a){if(l&1&&(n(0,"div",0)(1,"h1"),r(2,"Department Management"),i(),n(3,"div",1)(4,"mat-card",2)(5,"mat-card-header")(6,"mat-card-title"),r(7),i()(),n(8,"mat-card-content")(9,"form",3),_("ngSubmit",function(){return a.onSubmit()}),n(10,"mat-form-field",4)(11,"mat-label"),r(12,"Department Name"),i(),u(13,"input",5),d(14,ye,2,0,"mat-error",6)(15,Ie,2,0,"mat-error",6),i(),n(16,"mat-form-field",4)(17,"mat-label"),r(18,"Description"),i(),u(19,"textarea",7),d(20,we,2,0,"mat-error",6),i(),n(21,"div",8)(22,"label"),r(23,"Status:"),i(),n(24,"mat-checkbox",9),r(25,"Active"),i()(),n(26,"div",10)(27,"button",11),r(28),i(),n(29,"button",12),_("click",function(){return a.resetForm()}),r(30," Cancel "),i()()()()(),n(31,"mat-card",13)(32,"mat-card-header")(33,"mat-card-title"),r(34,"Departments"),i()(),n(35,"mat-card-content"),d(36,Fe,2,0,"div",14),n(37,"table",15),f(38,16),d(39,Pe,2,0,"th",17)(40,Te,2,1,"td",18),h(),f(41,19),d(42,ke,2,0,"th",17)(43,Oe,2,1,"td",18),h(),f(44,20),d(45,Ae,2,0,"th",21)(46,Le,2,1,"td",18),h(),f(47,22),d(48,Be,2,0,"th",17)(49,Ne,3,2,"td",18),h(),f(50,23),d(51,He,2,0,"th",21)(52,Re,7,2,"td",18),h(),d(53,Ve,1,0,"tr",24)(54,je,1,0,"tr",25),i(),d(55,$e,2,0,"div",26),i()()()()),l&2){let p,C,E;m(7),g(a.isEditing?"Edit Department":"Add Department"),m(2),s("formGroup",a.departmentForm),m(5),s("ngIf",(p=a.departmentForm.get("name"))==null?null:p.hasError("required")),m(),s("ngIf",(C=a.departmentForm.get("name"))==null?null:C.hasError("maxlength")),m(5),s("ngIf",(E=a.departmentForm.get("description"))==null?null:E.hasError("maxlength")),m(7),s("disabled",a.departmentForm.invalid),m(),S(" ",a.isEditing?"Update":"Save"," "),m(8),s("ngIf",a.isLoading),m(),s("dataSource",a.departments),m(16),s("matHeaderRowDef",a.displayedColumns),m(),s("matRowDefColumns",a.displayedColumns),m(),s("ngIf",a.departments.length===0&&!a.isLoading)}},dependencies:[T,F,P,R,L,k,O,A,B,N,$,V,j,Q,U,J,K,z,de,se,me,le,q,G,ge,_e,pe,oe,W,Y,ne,Z,X,ie,ee,te,re,ae,ce,he,ue,fe,Me,De,Se,xe],styles:[".departments-container[_ngcontent-%COMP%]{padding:20px}.departments-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin-bottom:20px;color:#1976d2}.departments-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 2fr;gap:20px}@media (max-width: 1200px){.departments-content[_ngcontent-%COMP%]{grid-template-columns:1fr}}.form-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%], .full-width[_ngcontent-%COMP%]{width:100%}.form-field-checkbox[_ngcontent-%COMP%]{margin:20px 0;display:flex;align-items:center}.form-field-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{margin-right:10px}.form-actions[_ngcontent-%COMP%]{display:flex;gap:10px;margin-top:20px}.spinner-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:20px}.status-active[_ngcontent-%COMP%]{color:green;font-weight:500}.status-inactive[_ngcontent-%COMP%]{color:red;font-weight:500}.no-data-message[_ngcontent-%COMP%]{text-align:center;padding:20px;color:#666;font-style:italic}"]})}}return e})();export{Ct as DepartmentsComponent};
