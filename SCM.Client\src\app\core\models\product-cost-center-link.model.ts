export interface ProductCostCenterLink {
  id: number;
  productId: number;
  productName: string;
  productCode: string;
  costCenterId: number;
  costCenterName: string;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  autoReorder: boolean;
  isActive: boolean;
  stockOnHand: number;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CostCenterWithLink {
  costCenterId: number;
  costCenterName: string;
  isLinked: boolean;
  linkId?: number;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  autoReorder: boolean;
  stockOnHand: number;
}

export interface CreateProductCostCenterLink {
  productId: number;
  costCenterId: number;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  autoReorder: boolean;
}

export interface UpdateProductCostCenterLink {
  id: number;
  productId: number;
  costCenterId: number;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  autoReorder: boolean;
  isActive: boolean;
}

export interface ProductCostCenterLinkListItem {
  id: number;
  productId: number;
  productName: string;
  productCode: string;
  costCenterId: number;
  costCenterName: string;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  autoReorder: boolean;
  isActive: boolean;
  createdAt: Date;
}

export interface ToggleLinkRequest {
  productId: number;
  costCenterId: number;
  isLinked: boolean;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
}
