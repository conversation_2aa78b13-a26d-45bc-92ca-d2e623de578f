using AutoMapper;
using SCM.Application.DTOs;
using SCM.Domain.Entities;

namespace SCM.Application.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // Department mappings
        CreateMap<Department, DepartmentDto>();
        CreateMap<CreateDepartmentDto, Department>();
        CreateMap<UpdateDepartmentDto, Department>();

        // UnitGroup mappings
        CreateMap<UnitGroup, UnitGroupDto>();
        CreateMap<CreateUnitGroupDto, UnitGroup>();
        CreateMap<UpdateUnitGroupDto, UnitGroup>();

        // Unit mappings
        CreateMap<Unit, UnitDto>()
            .ForMember(dest => dest.UnitGroupName, opt => opt.MapFrom(src => src.UnitGroup != null ? src.UnitGroup.Name : null));
        CreateMap<CreateUnitDto, Unit>();
        CreateMap<UpdateUnitDto, Unit>();

        // Product Group mappings
        CreateMap<ProductGroup, ProductGroupDto>()
            .ForMember(dest => dest.DepartmentName, opt => opt.MapFrom(src => src.Department != null ? src.Department.Name : null));
        CreateMap<CreateProductGroupDto, ProductGroup>();
        CreateMap<UpdateProductGroupDto, ProductGroup>();

        // Product SubGroup mappings
        CreateMap<ProductSubGroup, ProductSubGroupDto>()
            .ForMember(dest => dest.GroupName, opt => opt.MapFrom(src => src.Group != null ? src.Group.Name : null));
        CreateMap<CreateProductSubGroupDto, ProductSubGroup>();
        CreateMap<UpdateProductSubGroupDto, ProductSubGroup>();

        // Brand mappings
        CreateMap<Brand, BrandDto>();
        CreateMap<CreateBrandDto, Brand>();
        CreateMap<UpdateBrandDto, Brand>();

        // Tax mappings
        CreateMap<Tax, TaxDto>();
        CreateMap<CreateTaxDto, Tax>();
        CreateMap<UpdateTaxDto, Tax>();

        // Barcode mappings
        CreateMap<Barcode, BarcodeDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateBarcodeDto, Barcode>();
        CreateMap<UpdateBarcodeDto, Barcode>();

        // Product mappings
        CreateMap<Product, ProductDto>()
            .ForMember(dest => dest.BrandName, opt => opt.MapFrom(src => src.Brand != null ? src.Brand.Name : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.UnitGroupName, opt => opt.MapFrom(src => src.Unit != null && src.Unit.UnitGroup != null ? src.Unit.UnitGroup.Name : null))
            .ForMember(dest => dest.DepartmentName, opt => opt.MapFrom(src => src.Department != null ? src.Department.Name : null))
            .ForMember(dest => dest.GroupName, opt => opt.MapFrom(src => src.Group != null ? src.Group.Name : null))
            .ForMember(dest => dest.SubGroupName, opt => opt.MapFrom(src => src.SubGroup != null ? src.SubGroup.Name : null))
            .ForMember(dest => dest.TaxName, opt => opt.MapFrom(src => src.Tax != null ? src.Tax.Name : null))
            .ForMember(dest => dest.TaxRate, opt => opt.MapFrom(src => src.Tax != null ? (decimal?)src.Tax.Rate : null))
            .ForMember(dest => dest.SalesUnitName, opt => opt.MapFrom(src => src.SalesUnit != null ? src.SalesUnit.Name : null));
        CreateMap<CreateProductDto, Product>();
        CreateMap<UpdateProductDto, Product>();

        // Company mappings
        CreateMap<Company, CompanyDto>()
            .ForMember(dest => dest.LocationsCount, opt => opt.MapFrom(src => src.Locations.Count));
        CreateMap<Company, CompanyListDto>()
            .ForMember(dest => dest.LocationsCount, opt => opt.MapFrom(src => src.Locations.Count));
        CreateMap<CreateCompanyDto, Company>();
        CreateMap<UpdateCompanyDto, Company>();

        // Location mappings
        CreateMap<Location, LocationDto>()
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Company != null ? src.Company.Name : null))
            .ForMember(dest => dest.StoresCount, opt => opt.MapFrom(src => src.Stores.Count));
        CreateMap<CreateLocationDto, Location>();
        CreateMap<UpdateLocationDto, Location>();

        // Store mappings
        CreateMap<Store, StoreDto>()
            .ForMember(dest => dest.LocationName, opt => opt.MapFrom(src => src.Location != null ? src.Location.Name : null))
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Location != null && src.Location.Company != null ? src.Location.Company.Name : null))
            .ForMember(dest => dest.CostCentersCount, opt => opt.MapFrom(src => src.CostCenters.Count));
        CreateMap<CreateStoreDto, Store>();
        CreateMap<UpdateStoreDto, Store>();

        // CostCenterType mappings
        CreateMap<CostCenterType, CostCenterTypeDto>();
        CreateMap<CreateCostCenterTypeDto, CostCenterType>();
        CreateMap<UpdateCostCenterTypeDto, CostCenterType>();

        // CostCenter mappings
        CreateMap<CostCenter, CostCenterDto>()
            .ForMember(dest => dest.StoreName, opt => opt.MapFrom(src => src.Store != null ? src.Store.Name : null))
            .ForMember(dest => dest.LocationName, opt => opt.MapFrom(src => src.Store != null && src.Store.Location != null ? src.Store.Location.Name : null))
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Store != null && src.Store.Location != null && src.Store.Location.Company != null ? src.Store.Location.Company.Name : null))
            .ForMember(dest => dest.TypeName, opt => opt.MapFrom(src => src.Type != null ? src.Type.Name : src.TypeName));
        CreateMap<CostCenter, CostCenterListDto>()
            .ForMember(dest => dest.StoreName, opt => opt.MapFrom(src => src.Store != null ? src.Store.Name : null))
            .ForMember(dest => dest.LocationName, opt => opt.MapFrom(src => src.Store != null && src.Store.Location != null ? src.Store.Location.Name : null))
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Store != null && src.Store.Location != null && src.Store.Location.Company != null ? src.Store.Location.Company.Name : null))
            .ForMember(dest => dest.TypeName, opt => opt.MapFrom(src => src.Type != null ? src.Type.Name : src.TypeName));
        CreateMap<CreateCostCenterDto, CostCenter>();
        CreateMap<UpdateCostCenterDto, CostCenter>();

        // ProductCostCenterLink mappings
        CreateMap<ProductCostCenterLink, ProductCostCenterLinkDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter.Name));
        CreateMap<CreateProductCostCenterLinkDto, ProductCostCenterLink>();
        CreateMap<UpdateProductCostCenterLinkDto, ProductCostCenterLink>();

        // Batch mappings
        CreateMap<Batch, BatchDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateBatchDto, Batch>();
        CreateMap<UpdateBatchDto, Batch>();

        // StockOnHand mappings
        CreateMap<StockOnHand, StockOnHandDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter.Name))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));

        // StockTake mappings
        CreateMap<StockTakeHeader, StockTakeHeaderDto>()
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter.Name))
            .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy != null ? $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}".Trim() : null))
            .ForMember(dest => dest.CompletedByName, opt => opt.MapFrom(src => src.CompletedBy != null ? $"{src.CompletedBy.FirstName} {src.CompletedBy.LastName}".Trim() : null));
        CreateMap<CreateStockTakeHeaderDto, StockTakeHeader>();
        CreateMap<UpdateStockTakeHeaderDto, StockTakeHeader>();

        CreateMap<StockTakeDetail, StockTakeDetailDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.BatchNumber, opt => opt.MapFrom(src => src.Batch != null ? src.Batch.BatchNumber : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateStockTakeDetailDto, StockTakeDetail>()
            .ForMember(dest => dest.Variance, opt => opt.MapFrom(src => src.CountedQuantity - src.SystemQuantity));
        CreateMap<UpdateStockTakeDetailDto, StockTakeDetail>()
            .ForMember(dest => dest.Variance, opt => opt.MapFrom(src => src.CountedQuantity - src.SystemQuantity));

        // Purchase Order mappings
        CreateMap<PurchaseOrder, PurchaseOrderDto>()
            .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : null))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter != null ? src.CostCenter.Name : null));
        CreateMap<PurchaseOrder, PurchaseOrderListDto>()
            .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : null))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter != null ? src.CostCenter.Name : null));
        CreateMap<CreatePurchaseOrderDto, PurchaseOrder>();
        CreateMap<UpdatePurchaseOrderDto, PurchaseOrder>();

        // Purchase Order Detail mappings
        CreateMap<PurchaseOrderDetail, PurchaseOrderDetailDto>()
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product != null ? src.Product.Code : null))
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : null))
            .ForMember(dest => dest.UnitId, opt => opt.MapFrom(src => src.Product != null ? src.Product.UnitId : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Product != null && src.Product.Unit != null ? src.Product.Unit.Name : null));
        CreateMap<CreatePurchaseOrderDetailDto, PurchaseOrderDetail>();
        CreateMap<UpdatePurchaseOrderDetailDto, PurchaseOrderDetail>();

        // Recipe mappings
        CreateMap<Recipe, RecipeDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<Recipe, RecipeListDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.IngredientCount, opt => opt.MapFrom(src => src.Ingredients != null ? src.Ingredients.Count : 0));
        CreateMap<CreateRecipeDto, Recipe>();
        CreateMap<UpdateRecipeDto, Recipe>();

        // Recipe Ingredient mappings
        CreateMap<RecipeIngredient, RecipeIngredientDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.TotalCost, opt => opt.MapFrom(src => src.Cost.HasValue ? src.Cost.Value * src.Quantity : (decimal?)null));
        CreateMap<CreateRecipeIngredientDto, RecipeIngredient>();
        CreateMap<UpdateRecipeIngredientDto, RecipeIngredient>();

        // Stock Transfer mappings
        CreateMap<StockTransferHeader, StockTransferHeaderDto>()
            .ForMember(dest => dest.FromCostCenterName, opt => opt.MapFrom(src => src.FromCostCenter.Name))
            .ForMember(dest => dest.ToCostCenterName, opt => opt.MapFrom(src => src.ToCostCenter.Name))
            .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy != null ? $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}".Trim() : null))
            .ForMember(dest => dest.CompletedByName, opt => opt.MapFrom(src => src.CompletedBy != null ? $"{src.CompletedBy.FirstName} {src.CompletedBy.LastName}".Trim() : null));
        CreateMap<CreateStockTransferHeaderDto, StockTransferHeader>();
        CreateMap<UpdateStockTransferHeaderDto, StockTransferHeader>();

        CreateMap<StockTransferDetail, StockTransferDetailDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.BatchNumber, opt => opt.MapFrom(src => src.Batch != null ? src.Batch.BatchNumber : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateStockTransferDetailDto, StockTransferDetail>();
        CreateMap<UpdateStockTransferDetailDto, StockTransferDetail>();

        // Stock Adjustment mappings
        CreateMap<StockAdjustmentHeader, StockAdjustmentHeaderDto>()
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.CostCenter.Name))
            .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy != null ? $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}".Trim() : null))
            .ForMember(dest => dest.CompletedByName, opt => opt.MapFrom(src => src.CompletedBy != null ? $"{src.CompletedBy.FirstName} {src.CompletedBy.LastName}".Trim() : null));
        CreateMap<CreateStockAdjustmentHeaderDto, StockAdjustmentHeader>();
        CreateMap<UpdateStockAdjustmentHeaderDto, StockAdjustmentHeader>();

        CreateMap<StockAdjustmentDetail, StockAdjustmentDetailDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.BatchNumber, opt => opt.MapFrom(src => src.Batch != null ? src.Batch.BatchNumber : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null));
        CreateMap<CreateStockAdjustmentDetailDto, StockAdjustmentDetail>();
        CreateMap<UpdateStockAdjustmentDetailDto, StockAdjustmentDetail>();

        // Stock Request mappings
        CreateMap<StockRequestHeader, StockRequestHeaderDto>()
            .ForMember(dest => dest.FromCostCenterName, opt => opt.MapFrom(src => src.FromCostCenter.Name))
            .ForMember(dest => dest.ToCostCenterName, opt => opt.MapFrom(src => src.ToCostCenter.Name))
            .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedBy != null ? $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}".Trim() : null))
            .ForMember(dest => dest.ApprovedByName, opt => opt.MapFrom(src => src.ApprovedBy != null ? $"{src.ApprovedBy.FirstName} {src.ApprovedBy.LastName}".Trim() : null))
            .ForMember(dest => dest.CompletedByName, opt => opt.MapFrom(src => src.CompletedBy != null ? $"{src.CompletedBy.FirstName} {src.CompletedBy.LastName}".Trim() : null));
        CreateMap<CreateStockRequestHeaderDto, StockRequestHeader>();
        CreateMap<UpdateStockRequestHeaderDto, StockRequestHeader>();

        CreateMap<StockRequestDetail, StockRequestDetailDto>()
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.BatchNumber, opt => opt.MapFrom(src => src.Batch != null ? src.Batch.BatchNumber : null))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.Total, opt => opt.MapFrom(src => src.Price.HasValue ? src.Price.Value * src.Quantity : (decimal?)null));
        CreateMap<CreateStockRequestDetailDto, StockRequestDetail>();
        CreateMap<UpdateStockRequestDetailDto, StockRequestDetail>();

        // Supplier mappings
        CreateMap<Supplier, SupplierDto>();
        CreateMap<Supplier, SupplierListDto>();
        CreateMap<CreateSupplierDto, Supplier>();
        CreateMap<UpdateSupplierDto, Supplier>();

        // ProductRequest mappings

        // Transaction Stage Type mappings
        CreateMap<TransactionStageType, TransactionStageTypeDto>()
            .ForMember(dest => dest.StageTypeId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.Sequence, opt => opt.MapFrom(src => src.Sequence))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive));

        // New Transaction Stage Type Model mapping
        CreateMap<TransactionStageType, TransactionStageTypeModel>();

        // Transaction Header mappings
        CreateMap<TransactionHeader, TransactionHeaderDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ReferenceNumber, opt => opt.MapFrom(src => src.ReferenceNumber))
            .ForMember(dest => dest.TransactionTypeId, opt => opt.MapFrom(src => src.TransactionTypeId))
            .ForMember(dest => dest.TransactionTypeName, opt => opt.Ignore()) // We don't have TransactionType entity
            .ForMember(dest => dest.TransactionProcessId, opt => opt.Ignore()) // We don't use ProcessId anymore
            .ForMember(dest => dest.TransactionProcessName, opt => opt.Ignore()) // We don't use ProcessId anymore
            .ForMember(dest => dest.StageTypeId, opt => opt.MapFrom(src => src.StageTypeId))
            .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details))
            .ForMember(dest => dest.StageTypeName, opt => opt.Ignore()) // Will be set manually in service layer
            .ForMember(dest => dest.SourceCostCenterId, opt => opt.MapFrom(src => src.SourceCostCenterId))
            .ForMember(dest => dest.SourceCostCenterName, opt => opt.Ignore()) // Will be set manually in service layer
            .ForMember(dest => dest.DestinationCostCenterId, opt => opt.MapFrom(src => src.DestinationCostCenterId))
            .ForMember(dest => dest.DestinationCostCenterName, opt => opt.Ignore()) // Will be set manually in service layer
            .ForMember(dest => dest.SupplierId, opt => opt.MapFrom(src => src.SupplierId))
            .ForMember(dest => dest.SupplierName, opt => opt.Ignore()) // Will be set manually in service layer
            .ForMember(dest => dest.TransactionDate, opt => opt.MapFrom(src => src.TransactionDate))
            .ForMember(dest => dest.RequiredDate, opt => opt.MapFrom(src => src.RequiredDate))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Notes))
            .ForMember(dest => dest.TotalAmount, opt => opt.MapFrom(src => src.TotalAmount))
            .ForMember(dest => dest.TaxAmount, opt => opt.MapFrom(src => src.TaxAmount))
            .ForMember(dest => dest.DiscountAmount, opt => opt.MapFrom(src => src.DiscountAmount))
            .ForMember(dest => dest.NetAmount, opt => opt.MapFrom(src => src.SubTotal))
            .ForMember(dest => dest.CreatedById, opt => opt.MapFrom(src => src.CreatedById))
            .ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // We don't have access to CreatedBy
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.ApprovedById, opt => opt.MapFrom(src => src.ApprovedById))
            .ForMember(dest => dest.ApprovedByName, opt => opt.Ignore()) // We don't have access to ApprovedBy
            .ForMember(dest => dest.ApprovedAt, opt => opt.MapFrom(src => src.ApprovedDate))
            .ForMember(dest => dest.CompletedById, opt => opt.MapFrom(src => src.CompletedById))
            .ForMember(dest => dest.CompletedByName, opt => opt.Ignore()) // We don't have access to CompletedBy
            .ForMember(dest => dest.CompletedAt, opt => opt.MapFrom(src => src.CompletedAt))
            .ForMember(dest => dest.RelatedTransactionId, opt => opt.MapFrom(src => src.RelatedTransactionId))
            .ForMember(dest => dest.RelatedTransactionNumber, opt => opt.MapFrom(src => src.RelatedTransactionNumber))
            .ForMember(dest => dest.IsSkippedStep, opt => opt.MapFrom(src => src.IsSkippedStep));

        // Transaction Detail mappings
        CreateMap<TransactionDetail, TransactionDetailDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.TransactionHeaderId, opt => opt.MapFrom(src => src.TransactionId))
            .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product != null ? src.Product.Code : null))
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : null))
            .ForMember(dest => dest.BatchId, opt => opt.MapFrom(src => src.BatchId))
            .ForMember(dest => dest.BatchNumber, opt => opt.MapFrom(src => src.Batch != null ? src.Batch.BatchNumber : null))
            .ForMember(dest => dest.UnitId, opt => opt.MapFrom(src => src.UnitId))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.Quantity))
            .ForMember(dest => dest.UnitPrice, opt => opt.MapFrom(src => src.UnitPrice))
            .ForMember(dest => dest.TaxRate, opt => opt.MapFrom(src => src.TaxRate))
            .ForMember(dest => dest.TaxId, opt => opt.MapFrom(src => src.TaxId))
            .ForMember(dest => dest.TaxName, opt => opt.MapFrom(src => src.Tax != null ? src.Tax.Name : null))
            .ForMember(dest => dest.TaxAmount, opt => opt.MapFrom(src => src.TaxAmount))
            .ForMember(dest => dest.DiscountPercent, opt => opt.MapFrom(src => src.DiscountPercentage))
            .ForMember(dest => dest.DiscountAmount, opt => opt.MapFrom(src => src.DiscountAmount))
            .ForMember(dest => dest.TotalAmount, opt => opt.MapFrom(src => src.LineTotal))
            .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Notes))
            .ForMember(dest => dest.LineNumber, opt => opt.MapFrom(src => src.LineNumber));

        // Map TransactionHeader to ProductRequest
        CreateMap<TransactionHeader, ProductRequestListItem>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ReferenceNumber, opt => opt.MapFrom(src => src.ReferenceNumber))
            .ForMember(dest => dest.CostCenterId, opt => opt.MapFrom(src => src.SourceCostCenterId))
            .ForMember(dest => dest.CostCenterName, opt => opt.Ignore()) // Will be set manually in service layer
            .ForMember(dest => dest.RequestDate, opt => opt.MapFrom(src => src.TransactionDate))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.CreatedById, opt => opt.MapFrom(src => src.CreatedById))
            .ForMember(dest => dest.CreatedByName, opt => opt.Ignore()) // Ignore CreatedByName since we don't have access to CreatedBy
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt));

        CreateMap<TransactionHeaderDto, ProductRequestHeader>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ReferenceNumber, opt => opt.MapFrom(src => src.ReferenceNumber))
            .ForMember(dest => dest.CostCenterId, opt => opt.MapFrom(src => src.SourceCostCenterId))
            .ForMember(dest => dest.CostCenterName, opt => opt.MapFrom(src => src.SourceCostCenterName))
            .ForMember(dest => dest.RequestDate, opt => opt.MapFrom(src => src.TransactionDate))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Notes))
            .ForMember(dest => dest.CreatedById, opt => opt.MapFrom(src => src.CreatedById))
            .ForMember(dest => dest.CreatedByName, opt => opt.MapFrom(src => src.CreatedByName))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.ApprovedById, opt => opt.MapFrom(src => src.ApprovedById))
            .ForMember(dest => dest.ApprovedByName, opt => opt.MapFrom(src => src.ApprovedByName))
            .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details));

        CreateMap<TransactionDetail, ProductRequestDetail>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ProductRequestHeaderId, opt => opt.MapFrom(src => src.TransactionId))
            .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.Product.Code))
            .ForMember(dest => dest.BatchId, opt => opt.MapFrom(src => src.BatchId))
            .ForMember(dest => dest.UnitId, opt => opt.MapFrom(src => src.UnitId))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Unit != null ? src.Unit.Name : null))
            .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.Quantity))
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.UnitPrice))
            .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Notes));

        CreateMap<TransactionDetailDto, ProductRequestDetail>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ProductRequestHeaderId, opt => opt.MapFrom(src => src.TransactionHeaderId))
            .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.ProductId))
            .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.ProductName))
            .ForMember(dest => dest.ProductCode, opt => opt.MapFrom(src => src.ProductCode))
            .ForMember(dest => dest.BatchId, opt => opt.MapFrom(src => src.BatchId))
            .ForMember(dest => dest.UnitId, opt => opt.MapFrom(src => src.UnitId))
            .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.UnitName))
            .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.Quantity))
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.UnitPrice))
            .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Notes));
    }
}
