import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  ProductCostCenterLink, 
  CreateProductCostCenterLink, 
  UpdateProductCostCenterLink,
  ProductCostCenterLinkListItem 
} from '../models/product-cost-center-link.model';

@Injectable({
  providedIn: 'root'
})
export class ProductCostCenterLinkService {
  private readonly path = 'productcostcenterlinks';

  constructor(private apiService: ApiService) { }

  getAll(): Observable<ProductCostCenterLinkListItem[]> {
    return this.apiService.get<ProductCostCenterLinkListItem[]>(this.path);
  }

  getById(id: number): Observable<ProductCostCenterLink> {
    return this.apiService.get<ProductCostCenterLink>(`${this.path}/${id}`);
  }

  getByProductId(productId: number): Observable<ProductCostCenterLink[]> {
    return this.apiService.get<ProductCostCenterLink[]>(`${this.path}/product/${productId}`);
  }

  getByCostCenterId(costCenterId: number): Observable<ProductCostCenterLink[]> {
    return this.apiService.get<ProductCostCenterLink[]>(`${this.path}/costcenter/${costCenterId}`);
  }

  getByProductAndCostCenter(productId: number, costCenterId: number): Observable<ProductCostCenterLink> {
    return this.apiService.get<ProductCostCenterLink>(`${this.path}/product/${productId}/costcenter/${costCenterId}`);
  }

  create(link: CreateProductCostCenterLink): Observable<ProductCostCenterLink> {
    return this.apiService.post<ProductCostCenterLink>(this.path, link);
  }

  update(id: number, link: UpdateProductCostCenterLink): Observable<void> {
    return this.apiService.put<void>(`${this.path}/${id}`, link);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }
}
