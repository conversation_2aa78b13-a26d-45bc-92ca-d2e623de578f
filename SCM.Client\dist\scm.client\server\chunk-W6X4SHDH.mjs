import './polyfills.server.mjs';
import{a as S,c as Nt}from"./chunk-EGOXGUJD.mjs";import{a as ht,c as Mt}from"./chunk-F4JTRPZF.mjs";import{c as mt,h as ct}from"./chunk-3LTRCH3K.mjs";import{a as dt,b as lt}from"./chunk-CHXIZKOG.mjs";import"./chunk-KCBYLGU4.mjs";import"./chunk-R2YOBFRV.mjs";import{a as Ct,b as ft,c as xt,d as gt}from"./chunk-TMTYBZNO.mjs";import{a as j,b as z}from"./chunk-5V5SXYU2.mjs";import{a as W,b as X,c as Y,d as Z,e as tt,f as et,g as it,h as nt,i as ot,j as rt,l as at}from"./chunk-CUT3Q574.mjs";import{a as _t,b as ut}from"./chunk-S2DPCQFA.mjs";import{a as st,b as pt}from"./chunk-4QI52JLP.mjs";import{a as U,b as q,c as G,d as J,f as K,h as Q}from"./chunk-PWFZPCXU.mjs";import"./chunk-I44XXN6Y.mjs";import"./chunk-A3ICGYXF.mjs";import{_ as $,aa as A,ba as F,ca as H,da as V}from"./chunk-N6IFEGQ2.mjs";import{f as B,i as R}from"./chunk-46VHTMVE.mjs";import{Eb as y,Fb as o,Gb as _,Gc as P,Hb as N,Jc as O,Ka as a,Kc as k,La as h,Lc as I,Qb as w,Sb as v,Wb as b,Yb as L,Zb as D,ab as c,cb as l,ha as E,lb as i,mb as n,nb as f,oa as x,ob as s,pa as g,pb as p,rb as T,ub as M,wb as u}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";var vt=t=>["/transactions/credit-notes",t],St=t=>["/transactions/credit-notes",t,"edit"];function bt(t,r){t&1&&(i(0,"div",7),f(1,"mat-spinner"),i(2,"p"),o(3,"Loading credit notes..."),n()())}function Et(t,r){t&1&&(i(0,"div",8)(1,"mat-icon"),o(2,"receipt_long"),n(),i(3,"h3"),o(4,"No Credit Notes Found"),n(),i(5,"p"),o(6,"Create your first credit note to get started."),n(),i(7,"button",3)(8,"mat-icon"),o(9,"add"),n(),o(10," Create Credit Note "),n()())}function Tt(t,r){t&1&&(i(0,"th",25),o(1,"Transaction #"),n())}function yt(t,r){if(t&1&&(i(0,"td",26)(1,"a",27),o(2),n()()),t&2){let e=r.$implicit;a(),l("routerLink",v(2,vt,e.id)),a(),N(" ",e.transactionNumber," ")}}function wt(t,r){t&1&&(i(0,"th",25),o(1,"Reference #"),n())}function Lt(t,r){if(t&1&&(i(0,"td",26),o(1),n()),t&2){let e=r.$implicit;a(),_(e.referenceNumber||"-")}}function Dt(t,r){t&1&&(i(0,"th",25),o(1,"Supplier"),n())}function Pt(t,r){if(t&1&&(i(0,"td",26),o(1),n()),t&2){let e=r.$implicit;a(),_(e.supplierName||"-")}}function Ot(t,r){t&1&&(i(0,"th",25),o(1,"Cost Center"),n())}function kt(t,r){if(t&1&&(i(0,"td",26),o(1),n()),t&2){let e=r.$implicit;a(),_(e.sourceCostCenterName)}}function It(t,r){t&1&&(i(0,"th",25),o(1,"Date"),n())}function Bt(t,r){if(t&1&&(i(0,"td",26),o(1),b(2,"date"),n()),t&2){let e=r.$implicit;a(),_(L(2,1,e.transactionDate,"short"))}}function Rt(t,r){t&1&&(i(0,"th",25),o(1,"Status"),n())}function $t(t,r){if(t&1&&(i(0,"td",26)(1,"mat-chip",28),o(2),n()()),t&2){let e=r.$implicit,d=u(2);a(),l("color",d.getStatusColor(e.status)),a(),N(" ",e.status," ")}}function At(t,r){t&1&&(i(0,"th",25),o(1,"Reason"),n())}function Ft(t,r){if(t&1&&(i(0,"td",26)(1,"span",29),o(2),n()()),t&2){let e=r.$implicit;a(),l("matTooltip",e.reason),a(),_(e.reason)}}function Ht(t,r){t&1&&(i(0,"th",25),o(1,"Amount"),n())}function Vt(t,r){if(t&1&&(i(0,"td",26),o(1),b(2,"currency"),n()),t&2){let e=r.$implicit;a(),N(" ",D(2,1,e.totalAmount,"USD","symbol","1.2-2")," ")}}function jt(t,r){t&1&&(i(0,"th",25),o(1,"Related Transaction"),n())}function zt(t,r){if(t&1&&(i(0,"td",26),o(1),n()),t&2){let e=r.$implicit;a(),_(e.relatedTransactionNumber||"-")}}function Ut(t,r){t&1&&(i(0,"th",25),o(1,"Actions"),n())}function qt(t,r){if(t&1){let e=T();i(0,"td",26)(1,"button",30)(2,"mat-icon"),o(3,"more_vert"),n()(),i(4,"mat-menu",null,0)(6,"button",31)(7,"mat-icon"),o(8,"visibility"),n(),i(9,"span"),o(10,"View"),n()(),i(11,"button",32)(12,"mat-icon"),o(13,"edit"),n(),i(14,"span"),o(15,"Edit"),n()(),i(16,"button",33),M("click",function(){let m=x(e).$implicit,C=u(2);return g(C.completeCreditNote(m.id))}),i(17,"mat-icon"),o(18,"check_circle"),n(),i(19,"span"),o(20,"Complete"),n()(),i(21,"button",33),M("click",function(){let m=x(e).$implicit,C=u(2);return g(C.cancelCreditNote(m.id))}),i(22,"mat-icon"),o(23,"cancel"),n(),i(24,"span"),o(25,"Cancel"),n()(),f(26,"mat-divider"),i(27,"button",34),M("click",function(){let m=x(e).$implicit,C=u(2);return g(C.deleteCreditNote(m.id))}),i(28,"mat-icon"),o(29,"delete"),n(),i(30,"span"),o(31,"Delete"),n()()()()}if(t&2){let e=r.$implicit,d=y(5);a(),l("matMenuTriggerFor",d),a(5),l("routerLink",v(7,vt,e.id)),a(5),l("routerLink",v(9,St,e.id))("disabled",e.status!=="Draft"),a(5),l("disabled",e.status!=="Draft"),a(5),l("disabled",e.status==="Completed"),a(6),l("disabled",e.status==="Completed")}}function Gt(t,r){t&1&&f(0,"tr",35)}function Jt(t,r){t&1&&f(0,"tr",36)}function Kt(t,r){if(t&1&&(i(0,"div",9)(1,"table",10),s(2,11),c(3,Tt,2,0,"th",12)(4,yt,3,4,"td",13),p(),s(5,14),c(6,wt,2,0,"th",12)(7,Lt,2,1,"td",13),p(),s(8,15),c(9,Dt,2,0,"th",12)(10,Pt,2,1,"td",13),p(),s(11,16),c(12,Ot,2,0,"th",12)(13,kt,2,1,"td",13),p(),s(14,17),c(15,It,2,0,"th",12)(16,Bt,3,4,"td",13),p(),s(17,18),c(18,Rt,2,0,"th",12)(19,$t,3,2,"td",13),p(),s(20,19),c(21,At,2,0,"th",12)(22,Ft,3,2,"td",13),p(),s(23,20),c(24,Ht,2,0,"th",12)(25,Vt,3,6,"td",13),p(),s(26,21),c(27,jt,2,0,"th",12)(28,zt,2,1,"td",13),p(),s(29,22),c(30,Ut,2,0,"th",12)(31,qt,32,11,"td",13),p(),c(32,Gt,1,0,"tr",23)(33,Jt,1,0,"tr",24),n()()),t&2){let e=u();a(),l("dataSource",e.creditNotes),a(31),l("matHeaderRowDef",e.displayedColumns),a(),l("matRowDefColumns",e.displayedColumns)}}var be=(()=>{class t{constructor(e,d,m){this.creditNoteService=e,this.snackBar=d,this.dialog=m,this.creditNotes=[],this.loading=!1,this.displayedColumns=["transactionNumber","referenceNumber","supplierName","sourceCostCenterName","transactionDate","status","reason","totalAmount","relatedTransactionNumber","actions"]}ngOnInit(){this.loadCreditNotes()}loadCreditNotes(){this.loading=!0,this.creditNoteService.getAll().subscribe({next:e=>{this.creditNotes=e,this.loading=!1},error:e=>{console.error("Error loading credit notes:",e),this.snackBar.open("Error loading credit notes","Close",{duration:3e3}),this.loading=!1}})}getStatusColor(e){switch(e){case S.Draft:return"primary";case S.Completed:return"accent";case S.Cancelled:return"warn";default:return""}}completeCreditNote(e){confirm("Are you sure you want to complete this credit note? This action cannot be undone.")&&this.creditNoteService.complete(e).subscribe({next:()=>{this.snackBar.open("Credit note completed successfully","Close",{duration:3e3}),this.loadCreditNotes()},error:d=>{console.error("Error completing credit note:",d),this.snackBar.open("Error completing credit note","Close",{duration:3e3})}})}cancelCreditNote(e){let d=prompt("Please provide a reason for cancellation:");d!==null&&this.creditNoteService.cancel(e,d).subscribe({next:()=>{this.snackBar.open("Credit note cancelled successfully","Close",{duration:3e3}),this.loadCreditNotes()},error:m=>{console.error("Error cancelling credit note:",m),this.snackBar.open("Error cancelling credit note","Close",{duration:3e3})}})}deleteCreditNote(e){confirm("Are you sure you want to delete this credit note? This action cannot be undone.")&&this.creditNoteService.delete(e).subscribe({next:()=>{this.snackBar.open("Credit note deleted successfully","Close",{duration:3e3}),this.loadCreditNotes()},error:d=>{console.error("Error deleting credit note:",d),this.snackBar.open("Error deleting credit note","Close",{duration:3e3})}})}static{this.\u0275fac=function(d){return new(d||t)(h(Nt),h(st),h(mt))}}static{this.\u0275cmp=E({type:t,selectors:[["app-credit-note-list"]],standalone:!0,features:[w],decls:16,vars:3,consts:[["actionMenu","matMenu"],[1,"credit-note-list-container"],[1,"header-actions"],["mat-raised-button","","color","primary","routerLink","/transactions/credit-notes/new"],["class","loading-container",4,"ngIf"],["class","no-data",4,"ngIf"],["class","table-container",4,"ngIf"],[1,"loading-container"],[1,"no-data"],[1,"table-container"],["mat-table","",1,"credit-notes-table",3,"dataSource"],["matColumnDef","transactionNumber"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","referenceNumber"],["matColumnDef","supplierName"],["matColumnDef","sourceCostCenterName"],["matColumnDef","transactionDate"],["matColumnDef","status"],["matColumnDef","reason"],["matColumnDef","totalAmount"],["matColumnDef","relatedTransactionNumber"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["mat-header-cell",""],["mat-cell",""],[1,"transaction-link",3,"routerLink"],["selected","",3,"color"],[3,"matTooltip"],["mat-icon-button","",3,"matMenuTriggerFor"],["mat-menu-item","",3,"routerLink"],["mat-menu-item","",3,"routerLink","disabled"],["mat-menu-item","",3,"click","disabled"],["mat-menu-item","",1,"delete-action",3,"click","disabled"],["mat-header-row",""],["mat-row",""]],template:function(d,m){d&1&&(i(0,"div",1)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),o(4,"Credit Notes"),n(),i(5,"mat-card-subtitle"),o(6,"Manage credit notes for returned or adjusted goods"),n(),i(7,"div",2)(8,"button",3)(9,"mat-icon"),o(10,"add"),n(),o(11," New Credit Note "),n()()(),i(12,"mat-card-content"),c(13,bt,4,0,"div",4)(14,Et,11,0,"div",5)(15,Kt,34,3,"div",6),n()()()),d&2&&(a(13),l("ngIf",m.loading),a(),l("ngIf",!m.loading&&m.creditNotes.length===0),a(),l("ngIf",!m.loading&&m.creditNotes.length>0))},dependencies:[I,P,k,O,R,B,at,W,Y,it,Z,X,nt,tt,et,ot,rt,F,$,A,V,H,Q,U,G,K,J,q,Mt,ht,gt,ft,Ct,xt,lt,dt,ut,_t,pt,ct,z,j],styles:[".credit-note-list-container[_ngcontent-%COMP%]{padding:20px;max-width:1400px;margin:0 auto}mat-card[_ngcontent-%COMP%]{margin-bottom:20px}mat-card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}mat-card-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{margin-left:auto}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-bottom:20px}.no-data[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px 20px;text-align:center;color:#0009}.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:64px;width:64px;height:64px;margin-bottom:20px;opacity:.5}.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 10px;font-weight:400}.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 30px;font-size:14px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.credit-notes-table[_ngcontent-%COMP%]{width:100%;min-width:1000px}.credit-notes-table[_ngcontent-%COMP%]   .transaction-link[_ngcontent-%COMP%]{color:#1976d2;text-decoration:none;font-weight:500}.credit-notes-table[_ngcontent-%COMP%]   .transaction-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.credit-notes-table[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%]{font-size:12px;min-height:24px}.credit-notes-table[_ngcontent-%COMP%]   .delete-action[_ngcontent-%COMP%]{color:#f44336}@media (max-width: 768px){.credit-note-list-container[_ngcontent-%COMP%]{padding:10px}mat-card-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:15px}mat-card-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{margin-left:0;width:100%}.credit-notes-table[_ngcontent-%COMP%]{min-width:800px}}"]})}}return t})();export{be as CreditNoteListComponent};
