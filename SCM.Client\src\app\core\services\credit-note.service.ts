import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import {
  CreditNote,
  CreditNoteListItem,
  CreateCreditNote,
  UpdateCreditNote
} from '../models/credit-note.model';

@Injectable({
  providedIn: 'root'
})
export class CreditNoteService {
  private readonly path = 'creditnotes';

  constructor(private apiService: ApiService) { }

  // Credit Note CRUD operations
  getAll(): Observable<CreditNoteListItem[]> {
    return this.apiService.get<CreditNoteListItem[]>(this.path);
  }

  getById(id: number): Observable<CreditNote> {
    return this.apiService.get<CreditNote>(`${this.path}/${id}`);
  }

  create(creditNote: CreateCreditNote): Observable<CreditNote> {
    return this.apiService.post<CreditNote, CreateCreditNote>(this.path, creditNote);
  }

  update(id: number, creditNote: UpdateCreditNote): Observable<void> {
    return this.apiService.put<void, UpdateCreditNote>(`${this.path}/${id}`, creditNote);
  }

  delete(id: number): Observable<void> {
    return this.apiService.delete<void>(`${this.path}/${id}`);
  }

  // Credit Note workflow operations
  complete(id: number): Observable<void> {
    return this.apiService.post<void, any>(`${this.path}/${id}/complete`, {});
  }

  cancel(id: number, reason?: string): Observable<void> {
    return this.apiService.post<void, any>(`${this.path}/${id}/cancel`, { reason });
  }

  // Filter operations
  getByStatus(status: string): Observable<CreditNoteListItem[]> {
    return this.apiService.get<CreditNoteListItem[]>(`${this.path}/status/${status}`);
  }

  getBySupplierId(supplierId: number): Observable<CreditNoteListItem[]> {
    return this.apiService.get<CreditNoteListItem[]>(`${this.path}/supplier/${supplierId}`);
  }

  getByCostCenterId(costCenterId: number): Observable<CreditNoteListItem[]> {
    return this.apiService.get<CreditNoteListItem[]>(`${this.path}/costcenter/${costCenterId}`);
  }

  getByRelatedTransaction(transactionId: number): Observable<CreditNoteListItem[]> {
    return this.apiService.get<CreditNoteListItem[]>(`${this.path}/related/${transactionId}`);
  }

  // Create credit note from receiving transaction
  createFromReceiving(receivingTransactionId: number, creditNote: CreateCreditNote): Observable<CreditNote> {
    return this.apiService.post<CreditNote, CreateCreditNote>(
      `${this.path}/from-receiving/${receivingTransactionId}`,
      creditNote
    );
  }
}
