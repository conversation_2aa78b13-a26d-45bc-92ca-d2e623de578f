import './polyfills.server.mjs';
import{a as l}from"./chunk-4QI52JLP.mjs";import{Z as i,ca as c,m as a}from"./chunk-FFIKRRNO.mjs";var p=(()=>{class s{constructor(e){this.snackBar=e}handleError(e){let r="An unknown error occurred";return e.error instanceof ErrorEvent?(r=`Error: ${e.error.message}`,console.error("Client-side error:",e.error.message)):e.error instanceof SyntaxError&&e.error.message.includes("JSON")?(console.error("JSON parsing error:",e.error),r="Error processing server response. Please try again later."):(e.status===0?r="Could not connect to the server. Please check your internet connection.":e.status===400?r=this.handleBadRequest(e):e.status===401?r="You are not authorized to perform this action. Please log in again.":e.status===403?r="You do not have permission to perform this action.":e.status===404?r="The requested resource was not found.":e.status===500?r="A server error occurred. Please try again later.":r=`Error ${e.status}: ${e.statusText||"Unknown error"}`,console.error("Server-side error:",e)),this.snackBar.open(r,"Close",{duration:5e3,panelClass:["error-snackbar"]}),a(()=>new Error(r))}handleBadRequest(e){try{if(e.error&&typeof e.error=="object"&&e.error.message)return e.error.message;if(e.error&&typeof e.error=="string")try{let r=JSON.parse(e.error);if(r.message)return r.message}catch{return e.error}if(e.error&&typeof e.error=="object"&&e.error.errors){let r=e.error.errors,t=[];for(let n in r)if(r.hasOwnProperty(n)){let o=r[n];Array.isArray(o)?t.push(...o):typeof o=="string"&&t.push(o)}if(t.length>0)return t.join(". ")}return"Invalid request. Please check your input and try again."}catch(r){return console.error("Error handling bad request:",r),"Invalid request. Please check your input and try again."}}showSuccess(e){this.snackBar.open(e,"Close",{duration:3e3,panelClass:["success-snackbar"]})}showError(e){this.snackBar.open(e,"Close",{duration:5e3,panelClass:["error-snackbar"]})}showInfo(e){this.snackBar.open(e,"Close",{duration:3e3})}static{this.\u0275fac=function(r){return new(r||s)(c(l))}}static{this.\u0275prov=i({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})();export{p as a};
