using AutoMapper;
using Microsoft.EntityFrameworkCore;
using SCM.Application.DTOs;
using SCM.Application.Interfaces;
using SCM.Domain.Entities;
using SCM.Infrastructure.Data;

namespace SCM.Application.Services;

public class StockAdjustmentService : IStockAdjustmentService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IStockService _stockService;

    public StockAdjustmentService(
        ApplicationDbContext dbContext,
        IMapper mapper,
        IStockService stockService)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _stockService = stockService;
    }

    public async Task<IEnumerable<StockAdjustmentHeaderDto>> GetAllStockAdjustmentsAsync()
    {
        var stockAdjustments = await _dbContext.StockAdjustmentHeaders
            .Include(sa => sa.CostCenter)
            .Include(sa => sa.CreatedBy)
            .Include(sa => sa.CompletedBy)
            .OrderByDescending(sa => sa.AdjustmentDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockAdjustmentHeaderDto>>(stockAdjustments);
    }

    public async Task<StockAdjustmentHeaderDto?> GetStockAdjustmentByIdAsync(int id)
    {
        var stockAdjustment = await _dbContext.StockAdjustmentHeaders
            .Include(sa => sa.CostCenter)
            .Include(sa => sa.CreatedBy)
            .Include(sa => sa.CompletedBy)
            .Include(sa => sa.Details)
            .FirstOrDefaultAsync(sa => sa.Id == id);

        if (stockAdjustment == null)
            return null;

        // Load related data for stock adjustment details
        foreach (var detail in stockAdjustment.Details)
        {
            // Load product information
            if (detail.ProductId > 0)
            {
                detail.Product = await _dbContext.Products.FindAsync(detail.ProductId);
            }

            // Load batch information
            if (detail.BatchId.HasValue)
            {
                detail.Batch = await _dbContext.Batches.FindAsync(detail.BatchId.Value);
            }

            // Load unit information
            if (detail.UnitId.HasValue)
            {
                detail.Unit = await _dbContext.Units.FindAsync(detail.UnitId.Value);
            }
        }

        return _mapper.Map<StockAdjustmentHeaderDto>(stockAdjustment);
    }

    public async Task<IEnumerable<StockAdjustmentHeaderDto>> GetStockAdjustmentsByCostCenterIdAsync(int costCenterId)
    {
        var stockAdjustments = await _dbContext.StockAdjustmentHeaders
            .Include(sa => sa.CostCenter)
            .Include(sa => sa.CreatedBy)
            .Include(sa => sa.CompletedBy)
            .Where(sa => sa.CostCenterId == costCenterId)
            .OrderByDescending(sa => sa.AdjustmentDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockAdjustmentHeaderDto>>(stockAdjustments);
    }

    public async Task<IEnumerable<StockAdjustmentHeaderDto>> GetStockAdjustmentsByStatusAsync(string status)
    {
        var stockAdjustments = await _dbContext.StockAdjustmentHeaders
            .Include(sa => sa.CostCenter)
            .Include(sa => sa.CreatedBy)
            .Include(sa => sa.CompletedBy)
            .Where(sa => sa.Status == status)
            .OrderByDescending(sa => sa.AdjustmentDate)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockAdjustmentHeaderDto>>(stockAdjustments);
    }

    public async Task<StockAdjustmentHeaderDto> CreateStockAdjustmentAsync(CreateStockAdjustmentHeaderDto createStockAdjustmentHeaderDto)
    {
        // Generate reference number
        var referenceNumber = await GenerateReferenceNumberAsync();
        
        var stockAdjustment = _mapper.Map<StockAdjustmentHeader>(createStockAdjustmentHeaderDto);
        stockAdjustment.ReferenceNumber = referenceNumber;
        stockAdjustment.Status = "Draft";
        stockAdjustment.CreatedById = 1; // TODO: Get from current user
        
        _dbContext.StockAdjustmentHeaders.Add(stockAdjustment);
        await _dbContext.SaveChangesAsync();
        
        // Add details if provided
        if (createStockAdjustmentHeaderDto.Details != null && createStockAdjustmentHeaderDto.Details.Any())
        {
            foreach (var detailDto in createStockAdjustmentHeaderDto.Details)
            {
                await CreateStockAdjustmentDetailAsync(stockAdjustment.Id, detailDto);
            }
        }
        
        var createdStockAdjustment = await _dbContext.StockAdjustmentHeaders
            .Include(sa => sa.CostCenter)
            .Include(sa => sa.CreatedBy)
            .Include(sa => sa.Details)
                .ThenInclude(d => d.Product)
            .Include(sa => sa.Details)
                .ThenInclude(d => d.Batch)
            .Include(sa => sa.Details)
                .ThenInclude(d => d.Unit)
            .FirstOrDefaultAsync(sa => sa.Id == stockAdjustment.Id);
            
        return _mapper.Map<StockAdjustmentHeaderDto>(createdStockAdjustment);
    }

    public async Task UpdateStockAdjustmentAsync(UpdateStockAdjustmentHeaderDto updateStockAdjustmentHeaderDto)
    {
        var stockAdjustment = await _dbContext.StockAdjustmentHeaders
            .FirstOrDefaultAsync(sa => sa.Id == updateStockAdjustmentHeaderDto.Id);
            
        if (stockAdjustment == null)
            throw new KeyNotFoundException($"StockAdjustment with ID {updateStockAdjustmentHeaderDto.Id} not found.");
            
        if (stockAdjustment.Status != "Draft")
            throw new InvalidOperationException("Only stock adjustments in Draft status can be updated.");
            
        _mapper.Map(updateStockAdjustmentHeaderDto, stockAdjustment);
        
        _dbContext.StockAdjustmentHeaders.Update(stockAdjustment);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockAdjustmentAsync(int id)
    {
        var stockAdjustment = await _dbContext.StockAdjustmentHeaders
            .Include(sa => sa.Details)
            .FirstOrDefaultAsync(sa => sa.Id == id);
            
        if (stockAdjustment == null)
            throw new KeyNotFoundException($"StockAdjustment with ID {id} not found.");
            
        if (stockAdjustment.Status != "Draft")
            throw new InvalidOperationException("Only stock adjustments in Draft status can be deleted.");
            
        // Remove details first
        _dbContext.StockAdjustmentDetails.RemoveRange(stockAdjustment.Details);
        
        // Then remove header
        _dbContext.StockAdjustmentHeaders.Remove(stockAdjustment);
        
        await _dbContext.SaveChangesAsync();
    }

    public async Task CompleteStockAdjustmentAsync(CompleteStockAdjustmentDto completeStockAdjustmentDto)
    {
        var stockAdjustment = await _dbContext.StockAdjustmentHeaders
            .Include(sa => sa.Details)
                .ThenInclude(d => d.Product)
            .Include(sa => sa.Details)
                .ThenInclude(d => d.Batch)
            .FirstOrDefaultAsync(sa => sa.Id == completeStockAdjustmentDto.Id);
            
        if (stockAdjustment == null)
            throw new KeyNotFoundException($"StockAdjustment with ID {completeStockAdjustmentDto.Id} not found.");
            
        if (stockAdjustment.Status != "Draft")
            throw new InvalidOperationException("Only stock adjustments in Draft status can be completed.");
            
        if (!stockAdjustment.Details.Any())
            throw new InvalidOperationException("Cannot complete a stock adjustment with no details.");
            
        // Process the stock adjustment by adjusting stock
        foreach (var detail in stockAdjustment.Details)
        {
            // Adjust stock to the new quantity
            await _stockService.AdjustStockAsync(new StockAdjustmentDto
            {
                ProductId = detail.ProductId,
                CostCenterId = stockAdjustment.CostCenterId,
                UnitId = detail.UnitId,
                Quantity = detail.NewQuantity, // Set to the new quantity
                CostPrice = detail.CostPrice,
                Reason = detail.Reason,
                Notes = $"Adjustment #{stockAdjustment.ReferenceNumber}: {detail.Notes}"
            });
        }
        
        // Update stock adjustment header
        stockAdjustment.Status = "Completed";
        stockAdjustment.CompletedById = 1; // TODO: Get from current user
        stockAdjustment.CompletedAt = DateTime.UtcNow;
        stockAdjustment.Notes = completeStockAdjustmentDto.Notes ?? stockAdjustment.Notes;
        
        _dbContext.StockAdjustmentHeaders.Update(stockAdjustment);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CancelStockAdjustmentAsync(int id)
    {
        var stockAdjustment = await _dbContext.StockAdjustmentHeaders
            .FirstOrDefaultAsync(sa => sa.Id == id);
            
        if (stockAdjustment == null)
            throw new KeyNotFoundException($"StockAdjustment with ID {id} not found.");
            
        if (stockAdjustment.Status != "Draft")
            throw new InvalidOperationException("Only stock adjustments in Draft status can be cancelled.");
            
        stockAdjustment.Status = "Cancelled";
        
        _dbContext.StockAdjustmentHeaders.Update(stockAdjustment);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<StockAdjustmentDetailDto>> GetStockAdjustmentDetailsAsync(int stockAdjustmentHeaderId)
    {
        var details = await _dbContext.StockAdjustmentDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .Where(d => d.StockAdjustmentHeaderId == stockAdjustmentHeaderId)
            .OrderBy(d => d.Product.Name)
            .ToListAsync();
            
        return _mapper.Map<IEnumerable<StockAdjustmentDetailDto>>(details);
    }

    public async Task<StockAdjustmentDetailDto?> GetStockAdjustmentDetailByIdAsync(int id)
    {
        var detail = await _dbContext.StockAdjustmentDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        return detail != null ? _mapper.Map<StockAdjustmentDetailDto>(detail) : null;
    }

    public async Task<StockAdjustmentDetailDto> CreateStockAdjustmentDetailAsync(int stockAdjustmentHeaderId, CreateStockAdjustmentDetailDto createStockAdjustmentDetailDto)
    {
        var stockAdjustment = await _dbContext.StockAdjustmentHeaders
            .FirstOrDefaultAsync(sa => sa.Id == stockAdjustmentHeaderId);
            
        if (stockAdjustment == null)
            throw new KeyNotFoundException($"StockAdjustment with ID {stockAdjustmentHeaderId} not found.");
            
        if (stockAdjustment.Status != "Draft")
            throw new InvalidOperationException("Can only add details to stock adjustments in Draft status.");
            
        // Verify product exists
        var product = await _dbContext.Products.FindAsync(createStockAdjustmentDetailDto.ProductId);
        if (product == null)
            throw new KeyNotFoundException($"Product with ID {createStockAdjustmentDetailDto.ProductId} not found.");
            
        var detail = _mapper.Map<StockAdjustmentDetail>(createStockAdjustmentDetailDto);
        detail.StockAdjustmentHeaderId = stockAdjustmentHeaderId;
        
        _dbContext.StockAdjustmentDetails.Add(detail);
        await _dbContext.SaveChangesAsync();
        
        var createdDetail = await _dbContext.StockAdjustmentDetails
            .Include(d => d.Product)
            .Include(d => d.Batch)
            .Include(d => d.Unit)
            .FirstOrDefaultAsync(d => d.Id == detail.Id);
            
        return _mapper.Map<StockAdjustmentDetailDto>(createdDetail);
    }

    public async Task UpdateStockAdjustmentDetailAsync(UpdateStockAdjustmentDetailDto updateStockAdjustmentDetailDto)
    {
        var detail = await _dbContext.StockAdjustmentDetails
            .Include(d => d.StockAdjustmentHeader)
            .FirstOrDefaultAsync(d => d.Id == updateStockAdjustmentDetailDto.Id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockAdjustmentDetail with ID {updateStockAdjustmentDetailDto.Id} not found.");
            
        if (detail.StockAdjustmentHeader.Status != "Draft")
            throw new InvalidOperationException("Can only update details of stock adjustments in Draft status.");
            
        _mapper.Map(updateStockAdjustmentDetailDto, detail);
        
        _dbContext.StockAdjustmentDetails.Update(detail);
        await _dbContext.SaveChangesAsync();
    }

    public async Task DeleteStockAdjustmentDetailAsync(int id)
    {
        var detail = await _dbContext.StockAdjustmentDetails
            .Include(d => d.StockAdjustmentHeader)
            .FirstOrDefaultAsync(d => d.Id == id);
            
        if (detail == null)
            throw new KeyNotFoundException($"StockAdjustmentDetail with ID {id} not found.");
            
        if (detail.StockAdjustmentHeader.Status != "Draft")
            throw new InvalidOperationException("Can only delete details of stock adjustments in Draft status.");
            
        _dbContext.StockAdjustmentDetails.Remove(detail);
        await _dbContext.SaveChangesAsync();
    }

    private async Task<string> GenerateReferenceNumberAsync()
    {
        // Get the current date
        var today = DateTime.UtcNow;
        var year = today.Year.ToString().Substring(2); // Last 2 digits of year
        var month = today.Month.ToString().PadLeft(2, '0');
        var day = today.Day.ToString().PadLeft(2, '0');
        
        // Get the count of adjustments for today
        var todayAdjustments = await _dbContext.StockAdjustmentHeaders
            .CountAsync(sa => sa.CreatedAt.Date == today.Date);
        
        // Generate the reference number
        var sequence = (todayAdjustments + 1).ToString().PadLeft(3, '0');
        return $"ADJ-{year}{month}{day}-{sequence}";
    }
}
