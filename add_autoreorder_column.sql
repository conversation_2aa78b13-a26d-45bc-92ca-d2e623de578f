-- Add AutoReorder column to ProductCostCenterLink table
-- This script adds the missing AutoReorder column that the Entity Framework model expects

USE InventoryManagement;
GO

-- Check if the column already exists before adding it
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ProductCostCenterLink' 
    AND COLUMN_NAME = 'AutoReorder'
)
BEGIN
    -- Add the AutoReorder column with default value of 0 (false)
    ALTER TABLE ProductCostCenterLink
    ADD AutoReorder BIT NOT NULL DEFAULT 0;
    
    PRINT 'AutoReorder column added successfully to ProductCostCenterLink table';
END
ELSE
BEGIN
    PRINT 'AutoReorder column already exists in ProductCostCenterLink table';
END
GO

-- Verify the column was added
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ProductCostCenterLink'
ORDER BY ORDINAL_POSITION;
GO
