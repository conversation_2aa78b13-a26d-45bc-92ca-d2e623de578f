import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProductCostCenterLinkService } from '../../../core/services/product-cost-center-link.service';
import { ProductService } from '../../../core/services/product.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { ProductCostCenterLinkListItem } from '../../../core/models/product-cost-center-link.model';
import { Product } from '../../../core/models/product.model';
import { CostCenter } from '../../../core/models/cost-center.model';
import { finalize, catchError, of, forkJoin } from 'rxjs';

@Component({
  selector: 'app-product-cost-center-link-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatMenuModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './product-cost-center-link-list.component.html',
  styleUrls: ['./product-cost-center-link-list.component.scss']
})
export class ProductCostCenterLinkListComponent implements OnInit {
  displayedColumns: string[] = ['productCode', 'productName', 'costCenterName', 'minStock', 'maxStock', 'reorderPoint', 'autoReorder', 'actions'];
  links: ProductCostCenterLinkListItem[] = [];
  filteredLinks: ProductCostCenterLinkListItem[] = [];
  products: Product[] = [];
  costCenters: CostCenter[] = [];
  
  searchTerm: string = '';
  selectedProductId: number | null = null;
  selectedCostCenterId: number | null = null;
  isLoading: boolean = false;

  constructor(
    private productCostCenterLinkService: ProductCostCenterLinkService,
    private productService: ProductService,
    private costCenterService: CostCenterService,
    private snackBar: MatSnackBar,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isLoading = true;

    // Load all required data in parallel
    forkJoin({
      links: this.productCostCenterLinkService.getAll(),
      products: this.productService.getAll(),
      costCenters: this.costCenterService.getAllCostCenters()
    }).pipe(
      finalize(() => this.isLoading = false),
      catchError(error => {
        console.error('Error loading data', error);
        this.snackBar.open('Error loading data. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        return of({ links: [], products: [], costCenters: [] });
      })
    ).subscribe(data => {
      this.links = data.links;
      this.products = data.products;
      this.costCenters = data.costCenters;
      this.filteredLinks = [...this.links];
    });
  }

  applyFilter(): void {
    this.filteredLinks = this.links.filter(link => {
      const matchesSearch = !this.searchTerm || 
        link.productName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        link.productCode.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        link.costCenterName.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesProduct = !this.selectedProductId || link.productId === this.selectedProductId;
      const matchesCostCenter = !this.selectedCostCenterId || link.costCenterId === this.selectedCostCenterId;
      
      return matchesSearch && matchesProduct && matchesCostCenter;
    });
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedProductId = null;
    this.selectedCostCenterId = null;
    this.filteredLinks = [...this.links];
  }

  addLink(): void {
    this.router.navigate(['/products/cost-center-links/new']);
  }

  openManager(): void {
    this.router.navigate(['/products/cost-center-links/manager']);
  }

  editLink(id: number): void {
    this.router.navigate(['/products/cost-center-links', id]);
  }

  deleteLink(id: number): void {
    if (confirm('Are you sure you want to delete this product cost center link?')) {
      this.productCostCenterLinkService.delete(id).pipe(
        catchError(error => {
          console.error('Error deleting link', error);
          this.snackBar.open('Error deleting link. Please try again.', 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          return of(null);
        })
      ).subscribe(() => {
        this.snackBar.open('Link deleted successfully', 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadData(); // Reload the data
      });
    }
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.id === productId);
    return product ? product.name : 'Unknown Product';
  }

  getCostCenterName(costCenterId: number): string {
    const costCenter = this.costCenters.find(cc => cc.id === costCenterId);
    return costCenter ? costCenter.name : 'Unknown Cost Center';
  }

  formatNumber(value: number | null | undefined): string {
    return value != null ? value.toString() : '-';
  }
}
