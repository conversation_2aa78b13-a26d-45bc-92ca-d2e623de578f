namespace SCM.Application.DTOs;

public class StockOnHandDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public int? UnitId { get; set; }
    public string? UnitName { get; set; }
    public decimal Quantity { get; set; }
    public decimal? CostPrice { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class StockOnHandSummaryDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string ProductCode { get; set; } = string.Empty;
    public int CostCenterId { get; set; }
    public string CostCenterName { get; set; } = string.Empty;
    public decimal TotalQuantity { get; set; }
    public decimal? AverageCostPrice { get; set; }
    public string? UnitName { get; set; }
    public decimal? MinStock { get; set; }
    public decimal? MaxStock { get; set; }
    public decimal? ReorderPoint { get; set; }
    public bool IsLowStock { get; set; }
    public bool IsOverStock { get; set; }
    public bool NeedsReorder { get; set; }
}

public class StockAdjustmentDto
{
    public int ProductId { get; set; }
    public int CostCenterId { get; set; }
    public int? UnitId { get; set; }
    public decimal Quantity { get; set; }
    public decimal? CostPrice { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}


