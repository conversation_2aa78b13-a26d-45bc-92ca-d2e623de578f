import './polyfills.server.mjs';
import{a as He}from"./chunk-OCE4K5VP.mjs";import{b as $e,c as Ne}from"./chunk-F4JTRPZF.mjs";import{a as x,b as we,c as R,d as Ee,e as be}from"./chunk-4JYGXCOT.mjs";import{c as De,h as Ie}from"./chunk-3LTRCH3K.mjs";import"./chunk-CHXIZKOG.mjs";import{a as Re,b as ye}from"./chunk-J2FNDWGV.mjs";import{b as Le,c as Te}from"./chunk-GBVBP5JO.mjs";import{a as ve,e as Me,h as Se,i as xe}from"./chunk-KCBYLGU4.mjs";import{b as G,f as J,j as K,v as U,w as X}from"./chunk-R2YOBFRV.mjs";import{a as Ae,b as Fe,c as Be,d as Ve}from"./chunk-TMTYBZNO.mjs";import{a as ce,b as me,c as se,d as pe,e as de,f as ue,g as _e,h as fe,i as Ce,j as ge,l as he}from"./chunk-CUT3Q574.mjs";import{b as Pe}from"./chunk-S2DPCQFA.mjs";import{a as ke,b as Oe}from"./chunk-4QI52JLP.mjs";import{a as oe,c as re,h as le}from"./chunk-PWFZPCXU.mjs";import"./chunk-I44XXN6Y.mjs";import"./chunk-A3ICGYXF.mjs";import{U as Z,_ as ee,aa as te,ba as ie,ca as ne,da as ae}from"./chunk-N6IFEGQ2.mjs";import{e as q,i as Y}from"./chunk-46VHTMVE.mjs";import{Bb as y,Cb as w,Db as E,Eb as P,Ec as H,Fb as a,Fc as Q,Gb as f,Gc as W,Hb as b,Ib as A,J as I,Ka as m,Kc as j,La as M,Lc as z,Mb as L,Nb as T,Ob as D,Qb as F,Rb as B,Tb as V,Wb as $,Xb as N,ab as s,cb as p,ha as k,lb as i,mb as n,nb as S,oa as h,ob as u,pa as v,pb as _,rb as O,ub as d,wb as g}from"./chunk-FFIKRRNO.mjs";import"./chunk-VVCT4QZE.mjs";var Qe=()=>[10,25,50,100],We=(t,r)=>({"status-active":t,"status-inactive":r});function je(t,r){if(t&1&&(i(0,"mat-option",14),a(1),n()),t&2){let e=r.$implicit;p("value",e),m(),b(" ",e," ")}}function ze(t,r){t&1&&(i(0,"th",32),a(1,"ID"),n())}function qe(t,r){if(t&1&&(i(0,"td",33),a(1),n()),t&2){let e=r.$implicit;m(),f(e.id)}}function Ye(t,r){t&1&&(i(0,"th",32),a(1,"Name"),n())}function Ge(t,r){if(t&1&&(i(0,"td",33),a(1),n()),t&2){let e=r.$implicit;m(),f(e.name)}}function Je(t,r){t&1&&(i(0,"th",32),a(1,"Product"),n())}function Ke(t,r){if(t&1&&(i(0,"td",33)(1,"mat-chip-option",34),a(2),n()()),t&2){let e=r.$implicit;m(2),f(e.productName)}}function Ue(t,r){t&1&&(i(0,"th",32),a(1,"Yield"),n())}function Xe(t,r){if(t&1&&(i(0,"td",33),a(1),n()),t&2){let e=r.$implicit;m(),A("",e.yield," ",e.unitName,"")}}function Ze(t,r){t&1&&(i(0,"th",32),a(1,"Ingredients"),n())}function et(t,r){if(t&1&&(i(0,"td",33),a(1),n()),t&2){let e=r.$implicit;m(),f(e.ingredientCount)}}function tt(t,r){t&1&&(i(0,"th",32),a(1,"Cost"),n())}function it(t,r){if(t&1&&(i(0,"td",33),a(1),$(2,"currency"),n()),t&2){let e=r.$implicit;m(),f(N(2,1,e.cost))}}function nt(t,r){t&1&&(i(0,"th",32),a(1,"Status"),n())}function at(t,r){if(t&1&&(i(0,"td",35),a(1),n()),t&2){let e=r.$implicit;p("ngClass",V(2,We,e.isActive,!e.isActive)),m(),b(" ",e.isActive?"Active":"Inactive"," ")}}function ot(t,r){t&1&&(i(0,"th",36),a(1,"Actions"),n())}function rt(t,r){if(t&1){let e=O();i(0,"td",33)(1,"button",37)(2,"mat-icon"),a(3,"more_vert"),n()(),i(4,"mat-menu",null,0)(6,"button",38),d("click",function(){let o=h(e).$implicit,c=g(2);return v(c.viewRecipeDetails(o))}),i(7,"mat-icon"),a(8,"visibility"),n(),i(9,"span"),a(10,"View Details"),n()(),i(11,"button",38),d("click",function(){let o=h(e).$implicit,c=g(2);return v(c.editRecipe(o))}),i(12,"mat-icon"),a(13,"edit"),n(),i(14,"span"),a(15,"Edit"),n()(),i(16,"button",38),d("click",function(){let o=h(e).$implicit,c=g(2);return v(c.duplicateRecipe(o))}),i(17,"mat-icon"),a(18,"content_copy"),n(),i(19,"span"),a(20,"Duplicate"),n()(),i(21,"button",38),d("click",function(){let o=h(e).$implicit,c=g(2);return v(c.toggleRecipeStatus(o))}),i(22,"mat-icon"),a(23),n(),i(24,"span"),a(25),n()(),i(26,"button",38),d("click",function(){let o=h(e).$implicit,c=g(2);return v(c.deleteRecipe(o))}),i(27,"mat-icon"),a(28,"delete"),n(),i(29,"span"),a(30,"Delete"),n()()()()}if(t&2){let e=r.$implicit,l=P(5);m(),p("matMenuTriggerFor",l),m(22),f(e.isActive?"toggle_off":"toggle_on"),m(2),f(e.isActive?"Deactivate":"Activate")}}function lt(t,r){t&1&&S(0,"tr",39)}function ct(t,r){t&1&&S(0,"tr",40)}function mt(t,r){if(t&1&&(i(0,"div",15)(1,"table",16),u(2,17),s(3,ze,2,0,"th",18)(4,qe,2,1,"td",19),_(),u(5,20),s(6,Ye,2,0,"th",18)(7,Ge,2,1,"td",19),_(),u(8,21),s(9,Je,2,0,"th",18)(10,Ke,3,1,"td",19),_(),u(11,22),s(12,Ue,2,0,"th",18)(13,Xe,2,2,"td",19),_(),u(14,23),s(15,Ze,2,0,"th",18)(16,et,2,1,"td",19),_(),u(17,24),s(18,tt,2,0,"th",18)(19,it,3,3,"td",19),_(),u(20,25),s(21,nt,2,0,"th",18)(22,at,2,5,"td",26),_(),u(23,27),s(24,ot,2,0,"th",28)(25,rt,31,3,"td",19),_(),s(26,lt,1,0,"tr",29)(27,ct,1,0,"tr",30),n(),S(28,"mat-paginator",31),n()),t&2){let e=g();m(),p("dataSource",e.filteredRecipes),m(25),p("matHeaderRowDef",e.displayedColumns),m(),p("matRowDefColumns",e.displayedColumns),m(),p("pageSizeOptions",B(4,Qe))}}var Yt=(()=>{class t{constructor(e,l,o,c){this.recipeService=e,this.router=l,this.snackBar=o,this.dialog=c,this.displayedColumns=["id","name","productName","yield","ingredientCount","cost","isActive","actions"],this.recipes=[],this.filteredRecipes=[],this.searchTerm="",this.selectedStatus="",this.isLoading=!1,this.statusOptions=["All","Active","Inactive"],this.selectedCategory="",this.categories=[]}ngOnInit(){this.loadRecipes()}loadRecipes(){this.isLoading=!0,this.recipeService.getAll().pipe(I(()=>this.isLoading=!1)).subscribe({next:e=>{this.recipes=e,this.filteredRecipes=[...this.recipes]},error:e=>{console.error("Error loading recipes",e),this.snackBar.open("Error loading recipes","Close",{duration:3e3})}})}applyFilter(){this.filteredRecipes=this.recipes.filter(e=>{let l=this.searchTerm===""||e.name.toLowerCase().includes(this.searchTerm.toLowerCase())||e.id.toString().includes(this.searchTerm.toLowerCase())||e.productName.toLowerCase().includes(this.searchTerm.toLowerCase()),o=!0;return this.selectedStatus==="Active"?o=e.isActive:this.selectedStatus==="Inactive"&&(o=!e.isActive),l&&o})}resetFilters(){this.searchTerm="",this.selectedStatus="",this.filteredRecipes=[...this.recipes]}addRecipe(){this.router.navigate(["/recipes/new"])}editRecipe(e){this.router.navigate(["/recipes",e.id])}deleteRecipe(e){confirm(`Are you sure you want to delete recipe "${e.name}"?`)&&this.recipeService.delete(e.id).subscribe({next:()=>{this.snackBar.open("Recipe deleted successfully","Close",{duration:3e3}),this.loadRecipes()},error:l=>{console.error("Error deleting recipe",l),this.snackBar.open("Error deleting recipe","Close",{duration:3e3})}})}viewRecipeDetails(e){this.router.navigate(["/recipes",e.id])}duplicateRecipe(e){this.router.navigate(["/recipes/new"],{queryParams:{duplicate:e.id}})}toggleRecipeStatus(e){let l={id:e.id,name:e.name,productId:e.productId,yield:e.yield,unitId:void 0,instructions:void 0,notes:void 0,isSubRecipe:!1,isActive:!e.isActive};this.recipeService.update(e.id,l).subscribe({next:()=>{this.snackBar.open(`Recipe ${e.isActive?"deactivated":"activated"} successfully`,"Close",{duration:3e3}),this.loadRecipes()},error:o=>{console.error("Error updating recipe status",o),this.snackBar.open("Error updating recipe status","Close",{duration:3e3})}})}static{this.\u0275fac=function(l){return new(l||t)(M(He),M(q),M(ke),M(De))}}static{this.\u0275cmp=k({type:t,selectors:[["app-recipe-list"]],viewQuery:function(l,o){if(l&1&&(y(x,5),y(R,5)),l&2){let c;w(c=E())&&(o.paginator=c.first),w(c=E())&&(o.sort=c.first)}},standalone:!0,features:[F],decls:29,vars:4,consts:[["menu","matMenu"],[1,"page-container"],[1,"page-header"],["mat-raised-button","","color","primary",3,"click"],[1,"filter-card"],[1,"filter-container"],["appearance","outline"],["matInput","","placeholder","Search by name or ID",3,"ngModelChange","keyup","ngModel"],["matSuffix",""],[3,"ngModelChange","selectionChange","ngModel"],["value",""],[3,"value",4,"ngFor","ngForOf"],["mat-button","","color","primary",3,"click"],["class","table-container mat-elevation-z2",4,"ngIf"],[3,"value"],[1,"table-container","mat-elevation-z2"],["mat-table","","matSort","",1,"recipe-table",3,"dataSource"],["matColumnDef","id"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","name"],["matColumnDef","productName"],["matColumnDef","yield"],["matColumnDef","ingredientCount"],["matColumnDef","cost"],["matColumnDef","isActive"],["mat-cell","",3,"ngClass",4,"matCellDef"],["matColumnDef","actions"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["showFirstLastButtons","",3,"pageSizeOptions"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],["selected","","disableRipple",""],["mat-cell","",3,"ngClass"],["mat-header-cell",""],["mat-icon-button","","aria-label","Actions",3,"matMenuTriggerFor"],["mat-menu-item","",3,"click"],["mat-header-row",""],["mat-row",""]],template:function(l,o){l&1&&(i(0,"div",1)(1,"div",2)(2,"h1"),a(3,"Recipes"),n(),i(4,"button",3),d("click",function(){return o.addRecipe()}),i(5,"mat-icon"),a(6,"add"),n(),a(7," Add Recipe "),n()(),i(8,"mat-card",4)(9,"mat-card-content")(10,"div",5)(11,"mat-form-field",6)(12,"mat-label"),a(13,"Search"),n(),i(14,"input",7),D("ngModelChange",function(C){return T(o.searchTerm,C)||(o.searchTerm=C),C}),d("keyup",function(){return o.applyFilter()}),n(),i(15,"mat-icon",8),a(16,"search"),n()(),i(17,"mat-form-field",6)(18,"mat-label"),a(19,"Category"),n(),i(20,"mat-select",9),D("ngModelChange",function(C){return T(o.selectedCategory,C)||(o.selectedCategory=C),C}),d("selectionChange",function(){return o.applyFilter()}),i(21,"mat-option",10),a(22,"All Categories"),n(),s(23,je,2,2,"mat-option",11),n()(),i(24,"button",12),d("click",function(){return o.resetFilters()}),i(25,"mat-icon"),a(26,"clear"),n(),a(27," Reset "),n()()()(),s(28,mt,29,5,"div",13),n()),l&2&&(m(14),L("ngModel",o.searchTerm),m(6),L("ngModel",o.selectedCategory),m(3),p("ngForOf",o.categories),m(5),p("ngIf",!o.isLoading))},dependencies:[z,H,Q,W,j,Y,he,ce,se,_e,pe,me,fe,de,ue,Ce,ge,we,x,be,R,Ee,Te,Le,Se,ve,Me,xe,ie,ee,te,ae,ne,le,oe,re,Ve,Fe,Ae,Be,ye,Re,Z,Ne,$e,Oe,Pe,Ie,U,G,J,K,X],styles:[".page-container[_ngcontent-%COMP%]{padding:20px}.page-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.filter-card[_ngcontent-%COMP%]{margin-bottom:20px}.filter-container[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center;flex-wrap:wrap}.filter-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:200px}.table-container[_ngcontent-%COMP%]{overflow-x:auto;border-radius:4px}.recipe-table[_ngcontent-%COMP%]{width:100%}.mat-mdc-row[_ngcontent-%COMP%]:hover{background-color:#0000000a}.status-active[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.status-inactive[_ngcontent-%COMP%]{color:#f44336;font-weight:500}mat-chip-option[_ngcontent-%COMP%]{pointer-events:none!important}@media (max-width: 768px){.filter-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filter-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%}.page-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.page-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-top:10px}}"]})}}return t})();export{Yt as RecipeListComponent};
