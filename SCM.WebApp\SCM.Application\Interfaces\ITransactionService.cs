using SCM.Application.DTOs;

namespace SCM.Application.Interfaces;

public interface ITransactionService
{
    // General transaction methods
    Task<IEnumerable<TransactionHeaderDto>> GetAllTransactionsAsync();
    Task<TransactionHeaderDto?> GetTransactionByIdAsync(int id);
    // Removed GetTransactionsByProcessIdAsync method as we no longer use ProcessId
    Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByStageTypeIdAsync(int stageTypeId);
    Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByStatusAsync(string status);
    Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByCostCenterAsync(int costCenterId);
    Task<IEnumerable<TransactionHeaderDto>> GetTransactionsBySupplierAsync(int supplierId);
    Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByCustomerAsync(int customerId);
    Task<IEnumerable<TransactionHeaderDto>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate);

    Task<IEnumerable<TransactionStageTypeDto>> GetAllTransactionStageTypesAsync();

    // Product Request methods (Stage 1)
    Task<TransactionHeaderDto> CreateProductRequestAsync(CreateProductRequestDto createProductRequestDto, int userId);
    Task UpdateProductRequestAsync(int id, UpdateProductRequestDto updateProductRequestDto);
    Task SubmitProductRequestAsync(int id, string? notes = null);
    Task ApproveProductRequestAsync(int id, int userId, string? notes = null);
    Task RejectProductRequestAsync(int id, string reason);
    Task CancelProductRequestAsync(int id, string? reason = null);

    // Product Order methods (Stage 2)
    Task<TransactionHeaderDto> CreateProductOrderFromRequestAsync(int productRequestId, CreateProductOrderDto createProductOrderDto, int userId);
    Task<TransactionHeaderDto> CreateProductOrderAsync(CreateProductOrderDto createProductOrderDto, int userId);
    Task UpdateProductOrderAsync(int id, UpdateProductOrderDto updateProductOrderDto);
    Task SubmitProductOrderAsync(int id, string? notes = null);
    Task ApproveProductOrderAsync(int id, int userId, string? notes = null);
    Task RejectProductOrderAsync(int id, string reason);
    Task CancelProductOrderAsync(int id, string? reason = null);

    // Receiving methods (Stage 3)
    Task<TransactionHeaderDto> CreateReceivingFromOrderAsync(int productOrderId, CreateReceivingDto createReceivingDto, int userId);
    Task<TransactionHeaderDto> CreateReceivingAsync(CreateReceivingDto createReceivingDto, int userId);
    Task UpdateReceivingAsync(int id, UpdateReceivingDto updateReceivingDto);
    Task UpdateReceivingDetailAsync(int id, int detailId, UpdateReceivingDetailDto updateDetailDto);
    Task SubmitReceivingAsync(int id, string? notes = null);
    Task ApproveReceivingAsync(int id, int userId, string? notes = null);
    Task RejectReceivingAsync(int id, string reason);
    Task CompleteReceivingAsync(int id, int userId);
    Task CancelReceivingAsync(int id, string? reason = null);

    // Credit Note methods
    Task<TransactionHeaderDto> CreateCreditNoteAsync(CreateCreditNoteDto createCreditNoteDto, int userId);
    Task<TransactionHeaderDto> CreateCreditNoteFromReceivingAsync(int receivingTransactionId, CreateCreditNoteDto createCreditNoteDto, int userId);
    Task UpdateCreditNoteAsync(int id, UpdateCreditNoteDto updateCreditNoteDto);
    Task CompleteCreditNoteAsync(int id, int userId);
    Task CancelCreditNoteAsync(int id, string? reason = null);
    Task<IEnumerable<TransactionHeaderDto>> GetReceivingTransactionsAvailableForCreditNoteAsync();

    // Maintenance methods
    Task RecalculateAllTransactionTotalsAsync();
}
