.page-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;

    h1 {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 500;
      color: #333;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 1rem;
  }
}

.form-card {
  margin-bottom: 24px;

  mat-card-header {
    margin-bottom: 24px;

    mat-card-title {
      font-size: 1.25rem;
      font-weight: 500;
      color: #333;
    }

    mat-card-subtitle {
      color: #666;
      margin-top: 8px;
    }
  }

  mat-card-content {
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      mat-form-field {
        flex: 1;

        &.full-width {
          width: 100%;
        }
      }

      .checkbox-container {
        display: flex;
        flex-direction: column;
        flex: 1;
        padding-top: 8px;

        mat-checkbox {
          margin-bottom: 8px;
        }

        .checkbox-hint {
          font-size: 0.75rem;
          color: #666;
          line-height: 1.25;
        }
      }
    }

    .form-error {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #f44336;
      font-size: 0.875rem;
      margin-top: 16px;
      padding: 12px;
      background-color: #ffebee;
      border-radius: 4px;
      border-left: 4px solid #f44336;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  mat-card-actions {
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;

    button {
      margin-left: 8px;

      &:first-child {
        margin-left: 0;
      }

      mat-spinner {
        margin-right: 8px;
      }
    }
  }
}

// Form validation styles
mat-form-field {
  &.ng-invalid.ng-touched {
    .mat-form-field-outline {
      color: #f44336;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .page-header {
    .header-content {
      h1 {
        font-size: 1.5rem;
      }
    }
  }

  .form-card {
    mat-card-content {
      .form-row {
        flex-direction: column;
        gap: 0;

        mat-form-field {
          width: 100%;
          margin-bottom: 16px;
        }

        .checkbox-container {
          margin-bottom: 16px;
        }
      }
    }

    mat-card-actions {
      flex-direction: column-reverse;
      align-items: stretch;

      button {
        margin: 0 0 8px 0;
        width: 100%;

        &:first-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Success and error snackbar styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50;
    color: white;
  }

  .error-snackbar {
    background-color: #f44336;
    color: white;
  }
}
