.page-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 500;
    color: #333;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.filter-card {
  margin-bottom: 24px;

  .filter-container {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;

    mat-form-field {
      min-width: 200px;
      flex: 1;
    }

    button {
      margin-top: 8px;
    }
  }
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;

    mat-spinner {
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 1rem;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    p {
      margin: 0 0 24px 0;
      color: #666;
      font-size: 1.1rem;
    }
  }

  table {
    width: 100%;

    th {
      background-color: #f5f5f5;
      font-weight: 600;
      color: #333;
    }

    td, th {
      padding: 12px 16px;
      border-bottom: 1px solid #e0e0e0;
    }

    tr:hover {
      background-color: #f9f9f9;
    }

    .mat-column-productCode {
      width: 120px;
    }

    .mat-column-productName {
      min-width: 200px;
    }

    .mat-column-costCenterName {
      min-width: 150px;
    }

    .mat-column-minStock,
    .mat-column-maxStock,
    .mat-column-reorderPoint {
      width: 100px;
      text-align: right;
    }

    .mat-column-autoReorder {
      width: 120px;
      text-align: center;
    }

    .mat-column-actions {
      width: 80px;
      text-align: center;
    }
  }
}

.delete-action {
  color: #f44336;
}

// Responsive design
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    h1 {
      font-size: 1.5rem;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .filter-container {
    flex-direction: column;
    align-items: stretch;

    mat-form-field {
      min-width: unset;
      width: 100%;
    }

    button {
      align-self: flex-start;
    }
  }

  .table-container {
    overflow-x: auto;

    table {
      min-width: 800px;
    }
  }
}
