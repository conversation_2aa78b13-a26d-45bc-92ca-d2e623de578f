import { Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  // Auth routes
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'register',
    loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)
  },

  // Main application routes (protected)
  {
    path: '',
    loadComponent: () => import('./layout/main-layout/main-layout.component').then(m => m.MainLayoutComponent),
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },
      // Inventory Management
      {
        path: 'inventory/stock-request',
        loadComponent: () => import('./features/inventory/stock-request/stock-request.component').then(m => m.StockRequestComponent)
      },
      {
        path: 'inventory/stock-requests',
        loadComponent: () => import('./features/inventory/stock-request/stock-request-list/stock-request-list.component').then(m => m.StockRequestListComponent)
      },
      {
        path: 'inventory/stock-requests/new',
        loadComponent: () => import('./features/inventory/stock-request/stock-request-detail/stock-request-detail.component').then(m => m.StockRequestDetailComponent)
      },
      {
        path: 'inventory/stock-requests/:id',
        loadComponent: () => import('./features/inventory/stock-request/stock-request-detail/stock-request-detail.component').then(m => m.StockRequestDetailComponent)
      },
      {
        path: 'inventory/stock-adjustment',
        loadComponent: () => import('./features/inventory/stock-adjustment/stock-adjustment.component').then(m => m.StockAdjustmentComponent)
      },
      {
        path: 'inventory/stock-adjustments',
        loadComponent: () => import('./features/inventory/stock-adjustment/stock-adjustment-list/stock-adjustment-list.component').then(m => m.StockAdjustmentListComponent)
      },
      {
        path: 'inventory/stock-adjustments/new',
        loadComponent: () => import('./features/inventory/stock-adjustment/stock-adjustment-detail/stock-adjustment-detail.component').then(m => m.StockAdjustmentDetailComponent)
      },
      {
        path: 'inventory/stock-adjustments/:id',
        loadComponent: () => import('./features/inventory/stock-adjustment/stock-adjustment-detail/stock-adjustment-detail.component').then(m => m.StockAdjustmentDetailComponent)
      },
      {
        path: 'inventory/stock-taking',
        loadComponent: () => import('./features/inventory/stock-taking/stock-taking.component').then(m => m.StockTakingComponent)
      },
      {
        path: 'inventory/stock-transfer',
        loadComponent: () => import('./features/inventory/stock-transfer/stock-transfer.component').then(m => m.StockTransferComponent)
      },
      {
        path: 'inventory/stock-transfers',
        loadComponent: () => import('./features/inventory/stock-transfer/stock-transfer-list/stock-transfer-list.component').then(m => m.StockTransferListComponent)
      },
      {
        path: 'inventory/stock-transfers/new',
        loadComponent: () => import('./features/inventory/stock-transfer/stock-transfer-detail/stock-transfer-detail.component').then(m => m.StockTransferDetailComponent)
      },
      {
        path: 'inventory/stock-transfers/:id',
        loadComponent: () => import('./features/inventory/stock-transfer/stock-transfer-detail/stock-transfer-detail.component').then(m => m.StockTransferDetailComponent)
      },

      // Product Management
      {
        path: 'products',
        loadComponent: () => import('./features/products/product-list/product-list.component').then(m => m.ProductListComponent)
      },
      {
        path: 'products/new',
        loadComponent: () => import('./features/products/product-detail/product-detail.component').then(m => m.ProductDetailComponent)
      },
      {
        path: 'products/:id',
        loadComponent: () => import('./features/products/product-detail/product-detail.component').then(m => m.ProductDetailComponent)
      },
      {
        path: 'products/categories',
        loadComponent: () => import('./features/products/product-category/product-category.component').then(m => m.ProductCategoryComponent)
      },
      {
        path: 'products/cost-center-links',
        loadComponent: () => import('./features/products/product-cost-center-link/product-cost-center-link-list.component').then(m => m.ProductCostCenterLinkListComponent)
      },
      {
        path: 'products/cost-center-links/new',
        loadComponent: () => import('./features/products/product-cost-center-link/product-cost-center-link-detail.component').then(m => m.ProductCostCenterLinkDetailComponent)
      },
      {
        path: 'products/cost-center-links/:id',
        loadComponent: () => import('./features/products/product-cost-center-link/product-cost-center-link-detail.component').then(m => m.ProductCostCenterLinkDetailComponent)
      },

      // Transaction Management
      {
        path: 'transactions/product-requests',
        loadComponent: () => import('./features/transactions/product-request/product-request-list/product-request-list.component').then(m => m.ProductRequestListComponent)
      },
      {
        path: 'transactions/product-requests/new',
        loadComponent: () => import('./features/transactions/product-request/product-request.component').then(m => m.ProductRequestComponent)
      },
      {
        path: 'transactions/product-requests/:id/:mode',
        loadComponent: () => import('./features/transactions/product-request/product-request.component').then(m => m.ProductRequestComponent)
      },
      {
        path: 'transactions/purchase-orders',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-list/purchase-order-list.component').then(m => m.PurchaseOrderListComponent)
      },
      {
        path: 'procurement/suppliers',
        loadComponent: () => import('./features/procurement/supplier/supplier-list/supplier-list.component').then(m => m.SupplierListComponent)
      },
      {
        path: 'procurement/suppliers/new',
        loadComponent: () => import('./features/procurement/supplier/supplier-detail/supplier-detail.component').then(m => m.SupplierDetailComponent)
      },
      {
        path: 'procurement/suppliers/:id',
        loadComponent: () => import('./features/procurement/supplier/supplier-detail/supplier-detail.component').then(m => m.SupplierDetailComponent)
      },
      {
        path: 'transactions/purchase-orders/new',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-detail/purchase-order-detail.component').then(m => m.PurchaseOrderDetailComponent)
      },
      {
        path: 'transactions/purchase-orders/:id',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-view/purchase-order-view.component').then(m => m.PurchaseOrderViewComponent)
      },
      {
        path: 'transactions/purchase-orders/:id/:mode',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order-detail/purchase-order-detail.component').then(m => m.PurchaseOrderDetailComponent)
      },
      // New Purchase Order Component
      {
        path: 'transactions/purchase-orders/create',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order/purchase-order.component').then(m => m.PurchaseOrderComponent)
      },
      {
        path: 'transactions/purchase-orders/:id/edit',
        loadComponent: () => import('./features/transactions/purchase-orders/purchase-order/purchase-order.component').then(m => m.PurchaseOrderComponent)
      },
      // Receiving Routes
      {
        path: 'transactions/receiving',
        loadComponent: () => import('./features/transactions/receiving/receiving-list/receiving-list.component').then(m => m.ReceivingListComponent)
      },
      {
        path: 'transactions/receiving/new',
        loadComponent: () => import('./features/transactions/receiving/receiving.component').then(m => m.ReceivingComponent)
      },
      {
        path: 'transactions/receiving/from-order/:orderId',
        loadComponent: () => import('./features/transactions/receiving/receiving.component').then(m => m.ReceivingComponent)
      },
      {
        path: 'transactions/receiving/:id/edit',
        loadComponent: () => import('./features/transactions/receiving/receiving.component').then(m => m.ReceivingComponent)
      },
      {
        path: 'transactions/receiving/:id',
        loadComponent: () => import('./features/transactions/receiving/receiving.component').then(m => m.ReceivingComponent)
      },
      {
        path: 'transactions/sales',
        loadComponent: () => import('./features/transactions/sales/sales.component').then(m => m.SalesComponent)
      },
      // Credit Notes Routes
      {
        path: 'transactions/credit-notes',
        loadComponent: () => import('./features/transactions/credit-notes/credit-note-list/credit-note-list.component').then(m => m.CreditNoteListComponent)
      },
      {
        path: 'transactions/credit-notes/new',
        loadComponent: () => import('./features/transactions/credit-notes/credit-note-detail/credit-note-detail.component').then(m => m.CreditNoteDetailComponent)
      },
      {
        path: 'transactions/credit-notes/:id',
        loadComponent: () => import('./features/transactions/credit-notes/credit-note-detail/credit-note-detail.component').then(m => m.CreditNoteDetailComponent)
      },
      {
        path: 'transactions/credit-notes/:id/:mode',
        loadComponent: () => import('./features/transactions/credit-notes/credit-note-detail/credit-note-detail.component').then(m => m.CreditNoteDetailComponent)
      },

      // Recipe Management
      {
        path: 'recipes',
        loadComponent: () => import('./features/recipes/recipe-list/recipe-list.component').then(m => m.RecipeListComponent)
      },
      {
        path: 'recipes/new',
        loadComponent: () => import('./features/recipes/recipe-detail/recipe-detail.component').then(m => m.RecipeDetailComponent)
      },
      {
        path: 'recipes/:id',
        loadComponent: () => import('./features/recipes/recipe-detail/recipe-detail.component').then(m => m.RecipeDetailComponent)
      },

      // User Management
      {
        path: 'users',
        loadComponent: () => import('./features/users/user-list/user-list.component').then(m => m.UserListComponent),
        data: { roles: ['Admin'] }
      },
      {
        path: 'users/new',
        loadComponent: () => import('./features/users/user-detail/user-detail.component').then(m => m.UserDetailComponent),
        data: { roles: ['Admin'] }
      },
      {
        path: 'users/:id',
        loadComponent: () => import('./features/users/user-detail/user-detail.component').then(m => m.UserDetailComponent),
        data: { roles: ['Admin'] }
      },
      {
        path: 'users/roles',
        loadComponent: () => import('./features/users/role-management/role-management.component').then(m => m.RoleManagementComponent),
        data: { roles: ['Admin'] }
      },

      // Configuration
      {
        path: 'configuration/store-config',
        loadComponent: () => import('./features/configuration/store-config/store-config.component').then(m => m.StoreConfigComponent)
      },
      {
        path: 'configuration/stores',
        loadComponent: () => import('./features/configuration/stores/stores.component').then(m => m.StoresComponent)
      },
      {
        path: 'configuration/departments',
        loadComponent: () => import('./features/configuration/departments/departments.component').then(m => m.DepartmentsComponent),
        data: { roles: ['Admin'] }
      },
      {
        path: 'configuration/cost-centers',
        loadComponent: () => import('./features/configuration/cost-centers/cost-centers.component').then(m => m.CostCentersComponent)
      },
      {
        path: 'cost-centers/:id',
        loadComponent: () => import('./features/configuration/cost-centers/cost-center-detail/cost-center-detail.component').then(m => m.CostCenterDetailComponent)
      },
      {
        path: 'configuration/unit-groups',
        loadComponent: () => import('./features/configuration/unit-groups/unit-groups.component').then(m => m.UnitGroupsComponent)
      },
      {
        path: 'configuration/units',
        loadComponent: () => import('./features/configuration/units/units.component').then(m => m.UnitsComponent)
      },
      {
        path: 'companies',
        loadComponent: () => import('./features/business-structure/companies/companies.component').then(m => m.CompaniesComponent)
      },
      {
        path: 'companies/:id',
        loadComponent: () => import('./features/business-structure/companies/company-detail/company-detail.component').then(m => m.CompanyDetailComponent)
      },
      {
        path: 'locations',
        loadComponent: () => import('./features/business-structure/locations/locations.component').then(m => m.LocationsComponent)
      },
      {
        path: 'locations/:id',
        loadComponent: () => import('./features/business-structure/locations/location-detail/location-detail.component').then(m => m.LocationDetailComponent)
      }
    ]
  },
  {
    path: '**',
    redirectTo: ''
  }
];
