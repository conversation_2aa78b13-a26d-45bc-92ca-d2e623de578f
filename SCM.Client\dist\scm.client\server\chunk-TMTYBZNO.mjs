import './polyfills.server.mjs';
import{A as re,Ca as Ne,D as Ae,G as me,O as xe,P as Fe,c as De,f as Pe,na as Oe,qa as Le,t as Te,va as je,x as Ee,y as Se,za as R}from"./chunk-N6IFEGQ2.mjs";import{Ab as B,Bb as Ce,Cb as I,Db as C,F as S,H as X,Ka as Z,La as a,Lc as Re,Na as q,Pb as we,Qb as se,S as A,T as W,Ta as $,U as x,Va as ve,Ya as J,_ as he,a as P,aa as f,ab as ee,bb as M,cb as te,da as G,eb as be,f as T,fb as Me,ga as r,gd as oe,ha as K,hb as ye,hd as H,i as U,ia as pe,ja as _e,kd as c,l as Q,lb as y,lc as w,ld as ae,mb as L,nb as ie,nd as Y,oa as v,pa as b,pc as k,qa as ge,rb as Ie,ub as j,wa as F,wb as l,wc as ke,xa as O,xb as ne,y as E,ya as fe,yb as N,z as g}from"./chunk-FFIKRRNO.mjs";import{a as D,b as de}from"./chunk-VVCT4QZE.mjs";var Ke=["mat-menu-item",""],Ze=[[["mat-icon"],["","matMenuItemIcon",""]],"*"],qe=["mat-icon, [matMenuItemIcon]","*"];function $e(o,Ve){o&1&&(ge(),y(0,"svg",2),ie(1,"polygon",3),L())}var Je=["*"];function et(o,Ve){if(o&1){let e=Ie();y(0,"div",0),j("keydown",function(i){v(e);let n=l();return b(n._handleKeydown(i))})("click",function(){v(e);let i=l();return b(i.closed.emit("click"))})("@transformMenu.start",function(i){v(e);let n=l();return b(n._onAnimationStart(i))})("@transformMenu.done",function(i){v(e);let n=l();return b(n._onAnimationDone(i))}),y(1,"div",1),N(2),L()()}if(o&2){let e=l();Me(e._classList),te("id",e.panelId)("@transformMenu",e._panelAnimationState),M("aria-label",e.ariaLabel||null)("aria-labelledby",e.ariaLabelledby||null)("aria-describedby",e.ariaDescribedby||null)}}var ce=new f("MAT_MENU_PANEL"),le=(()=>{class o{constructor(e,t,i,n,s){this._elementRef=e,this._document=t,this._focusMonitor=i,this._parentMenu=n,this._changeDetectorRef=s,this.role="menuitem",this.disabled=!1,this.disableRipple=!1,this._hovered=new T,this._focused=new T,this._highlighted=!1,this._triggersSubmenu=!1,n?.addItem?.(this)}focus(e,t){this._focusMonitor&&e?this._focusMonitor.focusVia(this._getHostElement(),e,t):this._getHostElement().focus(t),this._focused.next(this)}ngAfterViewInit(){this._focusMonitor&&this._focusMonitor.monitor(this._elementRef,!1)}ngOnDestroy(){this._focusMonitor&&this._focusMonitor.stopMonitoring(this._elementRef),this._parentMenu&&this._parentMenu.removeItem&&this._parentMenu.removeItem(this),this._hovered.complete(),this._focused.complete()}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._elementRef.nativeElement}_checkDisabled(e){this.disabled&&(e.preventDefault(),e.stopPropagation())}_handleMouseEnter(){this._hovered.next(this)}getLabel(){let e=this._elementRef.nativeElement.cloneNode(!0),t=e.querySelectorAll("mat-icon, .material-icons");for(let i=0;i<t.length;i++)t[i].remove();return e.textContent?.trim()||""}_setHighlighted(e){this._highlighted=e,this._changeDetectorRef?.markForCheck()}_setTriggersSubmenu(e){this._triggersSubmenu=e,this._changeDetectorRef?.markForCheck()}_hasFocus(){return this._document&&this._document.activeElement===this._getHostElement()}static{this.\u0275fac=function(t){return new(t||o)(a(F),a(ke),a(re),a(ce,8),a(w))}}static{this.\u0275cmp=K({type:o,selectors:[["","mat-menu-item",""]],hostAttrs:[1,"mat-mdc-menu-item","mat-mdc-focus-indicator"],hostVars:8,hostBindings:function(t,i){t&1&&j("click",function(s){return i._checkDisabled(s)})("mouseenter",function(){return i._handleMouseEnter()}),t&2&&(M("role",i.role)("tabindex",i._getTabIndex())("aria-disabled",i.disabled)("disabled",i.disabled||null),be("mat-mdc-menu-item-highlighted",i._highlighted)("mat-mdc-menu-item-submenu-trigger",i._triggersSubmenu))},inputs:{role:"role",disabled:[r.HasDecoratorInputTransform,"disabled","disabled",k],disableRipple:[r.HasDecoratorInputTransform,"disableRipple","disableRipple",k]},exportAs:["matMenuItem"],standalone:!0,features:[J,se],attrs:Ke,ngContentSelectors:qe,decls:5,vars:3,consts:[[1,"mat-mdc-menu-item-text"],["matRipple","",1,"mat-mdc-menu-ripple",3,"matRippleDisabled","matRippleTrigger"],["viewBox","0 0 5 10","focusable","false","aria-hidden","true",1,"mat-mdc-menu-submenu-icon"],["points","0,0 5,5 0,10"]],template:function(t,i){t&1&&(ne(Ze),N(0),y(1,"span",0),N(2,1),L(),ie(3,"div",1),ee(4,$e,2,0,":svg:svg",2)),t&2&&(Z(3),te("matRippleDisabled",i.disableRipple||i.disabled)("matRippleTrigger",i._getHostElement()),Z(),ye(4,i._triggersSubmenu?4:-1))},dependencies:[xe],encapsulation:2,changeDetection:0})}}return o})();var tt=new f("MatMenuContent");var V={transformMenu:oe("transformMenu",[ae("void",c({opacity:0,transform:"scale(0.8)"})),Y("void => enter",H("120ms cubic-bezier(0, 0, 0.2, 1)",c({opacity:1,transform:"scale(1)"}))),Y("* => void",H("100ms 25ms linear",c({opacity:0})))]),fadeInItems:oe("fadeInItems",[ae("showing",c({opacity:1})),Y("void => *",[c({opacity:0}),H("400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)")])])},Ht=V.fadeInItems,Yt=V.transformMenu,it=0,nt=new f("mat-menu-default-options",{providedIn:"root",factory:st});function st(){return{overlapTrigger:!1,xPosition:"after",yPosition:"below",backdropClass:"cdk-overlay-transparent-backdrop"}}var z=(()=>{class o{get xPosition(){return this._xPosition}set xPosition(e){this._xPosition=e,this.setPositionClasses()}get yPosition(){return this._yPosition}set yPosition(e){this._yPosition=e,this.setPositionClasses()}set panelClass(e){let t=this._previousPanelClass,i=D({},this._classList);t&&t.length&&t.split(" ").forEach(n=>{i[n]=!1}),this._previousPanelClass=e,e&&e.length&&(e.split(" ").forEach(n=>{i[n]=!0}),this._elementRef.nativeElement.className=""),this._classList=i}get classList(){return this.panelClass}set classList(e){this.panelClass=e}constructor(e,t,i,n){this._elementRef=e,this._ngZone=t,this._changeDetectorRef=n,this._elevationPrefix="mat-elevation-z",this._baseElevation=8,this._directDescendantItems=new fe,this._classList={},this._panelAnimationState="void",this._animationDone=new T,this.closed=new O,this.close=this.closed,this.panelId=`mat-menu-panel-${it++}`,this.overlayPanelClass=i.overlayPanelClass||"",this._xPosition=i.xPosition,this._yPosition=i.yPosition,this.backdropClass=i.backdropClass,this.overlapTrigger=i.overlapTrigger,this.hasBackdrop=i.hasBackdrop}ngOnInit(){this.setPositionClasses()}ngAfterContentInit(){this._updateDirectDescendants(),this._keyManager=new Te(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd(),this._keyManager.tabOut.subscribe(()=>this.closed.emit("tab")),this._directDescendantItems.changes.pipe(A(this._directDescendantItems),W(e=>E(...e.map(t=>t._focused)))).subscribe(e=>this._keyManager.updateActiveItem(e)),this._directDescendantItems.changes.subscribe(e=>{let t=this._keyManager;if(this._panelAnimationState==="enter"&&t.activeItem?._hasFocus()){let i=e.toArray(),n=Math.max(0,Math.min(i.length-1,t.activeItemIndex||0));i[n]&&!i[n].disabled?t.setActiveItem(n):t.setNextItemActive()}})}ngOnDestroy(){this._keyManager?.destroy(),this._directDescendantItems.destroy(),this.closed.complete(),this._firstItemFocusSubscription?.unsubscribe()}_hovered(){return this._directDescendantItems.changes.pipe(A(this._directDescendantItems),W(t=>E(...t.map(i=>i._hovered))))}addItem(e){}removeItem(e){}_handleKeydown(e){let t=e.keyCode,i=this._keyManager;switch(t){case 27:Pe(e)||(e.preventDefault(),this.closed.emit("keydown"));break;case 37:this.parentMenu&&this.direction==="ltr"&&this.closed.emit("keydown");break;case 39:this.parentMenu&&this.direction==="rtl"&&this.closed.emit("keydown");break;default:(t===38||t===40)&&i.setFocusOrigin("keyboard"),i.onKeydown(e);return}e.stopPropagation()}focusFirstItem(e="program"){this._firstItemFocusSubscription?.unsubscribe(),this._firstItemFocusSubscription=this._ngZone.onStable.pipe(S(1)).subscribe(()=>{let t=null;if(this._directDescendantItems.length&&(t=this._directDescendantItems.first._getHostElement().closest('[role="menu"]')),!t||!t.contains(document.activeElement)){let i=this._keyManager;i.setFocusOrigin(e).setFirstItemActive(),!i.activeItem&&t&&t.focus()}})}resetActiveItem(){this._keyManager.setActiveItem(-1)}setElevation(e){let t=Math.min(this._baseElevation+e,24),i=`${this._elevationPrefix}${t}`,n=Object.keys(this._classList).find(s=>s.startsWith(this._elevationPrefix));if(!n||n===this._previousElevation){let s=D({},this._classList);this._previousElevation&&(s[this._previousElevation]=!1),s[i]=!0,this._previousElevation=i,this._classList=s}}setPositionClasses(e=this.xPosition,t=this.yPosition){this._classList=de(D({},this._classList),{"mat-menu-before":e==="before","mat-menu-after":e==="after","mat-menu-above":t==="above","mat-menu-below":t==="below"}),this._changeDetectorRef?.markForCheck()}_startAnimation(){this._panelAnimationState="enter"}_resetAnimation(){this._panelAnimationState="void"}_onAnimationDone(e){this._animationDone.next(e),this._isAnimating=!1}_onAnimationStart(e){this._isAnimating=!0,e.toState==="enter"&&this._keyManager.activeItemIndex===0&&(e.element.scrollTop=0)}_updateDirectDescendants(){this._allItems.changes.pipe(A(this._allItems)).subscribe(e=>{this._directDescendantItems.reset(e.filter(t=>t._parentMenu===this)),this._directDescendantItems.notifyOnChanges()})}static{this.\u0275fac=function(t){return new(t||o)(a(F),a($),a(nt),a(w))}}static{this.\u0275cmp=K({type:o,selectors:[["mat-menu"]],contentQueries:function(t,i,n){if(t&1&&(B(n,tt,5),B(n,le,5),B(n,le,4)),t&2){let s;I(s=C())&&(i.lazyContent=s.first),I(s=C())&&(i._allItems=s),I(s=C())&&(i.items=s)}},viewQuery:function(t,i){if(t&1&&Ce(q,5),t&2){let n;I(n=C())&&(i.templateRef=n.first)}},hostVars:3,hostBindings:function(t,i){t&2&&M("aria-label",null)("aria-labelledby",null)("aria-describedby",null)},inputs:{backdropClass:"backdropClass",ariaLabel:[r.None,"aria-label","ariaLabel"],ariaLabelledby:[r.None,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[r.None,"aria-describedby","ariaDescribedby"],xPosition:"xPosition",yPosition:"yPosition",overlapTrigger:[r.HasDecoratorInputTransform,"overlapTrigger","overlapTrigger",k],hasBackdrop:[r.HasDecoratorInputTransform,"hasBackdrop","hasBackdrop",e=>e==null?null:k(e)],panelClass:[r.None,"class","panelClass"],classList:"classList"},outputs:{closed:"closed",close:"close"},exportAs:["matMenu"],standalone:!0,features:[we([{provide:ce,useExisting:o}]),J,se],ngContentSelectors:Je,decls:1,vars:0,consts:[["tabindex","-1","role","menu",1,"mat-mdc-menu-panel","mat-mdc-elevation-specific",3,"keydown","click","id"],[1,"mat-mdc-menu-content"]],template:function(t,i){t&1&&(ne(),ee(0,et,3,7,"ng-template"))},styles:['mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;list-style-type:none}.mat-mdc-menu-content:focus{outline:none}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font);line-height:var(--mat-menu-item-label-text-line-height);font-size:var(--mat-menu-item-label-text-size);letter-spacing:var(--mat-menu-item-label-text-tracking);font-weight:var(--mat-menu-item-label-text-weight)}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape);background-color:var(--mat-menu-container-color);will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-divider{color:var(--mat-menu-divider-color);margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;align-items:center;min-height:48px}.mat-mdc-menu-item:focus{outline:none}[dir=rtl] .mat-mdc-menu-item,.mat-mdc-menu-item[dir=rtl]{padding-left:var(--mat-menu-item-trailing-spacing);padding-right:var(--mat-menu-item-leading-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]),.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon])[dir=rtl]{padding-left:var(--mat-menu-item-with-icon-trailing-spacing);padding-right:var(--mat-menu-item-with-icon-leading-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color)}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color)}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:"";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1)}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}'],encapsulation:2,data:{animation:[V.transformMenu,V.fadeInItems]},changeDetection:0})}}return o})(),ze=new f("mat-menu-scroll-strategy",{providedIn:"root",factory:()=>{let o=G(R);return()=>o.scrollStrategies.reposition()}});function ot(o){return()=>o.scrollStrategies.reposition()}var at={provide:ze,deps:[R],useFactory:ot},Be=De({passive:!0});var zt=(()=>{class o{get _deprecatedMatMenuTriggerFor(){return this.menu}set _deprecatedMatMenuTriggerFor(e){this.menu=e}get menu(){return this._menu}set menu(e){e!==this._menu&&(this._menu=e,this._menuCloseSubscription.unsubscribe(),e&&(this._parentMaterialMenu,this._menuCloseSubscription=e.close.subscribe(t=>{this._destroyMenu(t),(t==="click"||t==="tab")&&this._parentMaterialMenu&&this._parentMaterialMenu.closed.emit(t)})),this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu()))}constructor(e,t,i,n,s,m,u,d,h){this._overlay=e,this._element=t,this._viewContainerRef=i,this._menuItemInstance=m,this._dir=u,this._focusMonitor=d,this._ngZone=h,this._overlayRef=null,this._menuOpen=!1,this._closingActionsSubscription=P.EMPTY,this._hoverSubscription=P.EMPTY,this._menuCloseSubscription=P.EMPTY,this._changeDetectorRef=G(w),this._handleTouchStart=p=>{Se(p)||(this._openedBy="touch")},this._openedBy=void 0,this.restoreFocus=!0,this.menuOpened=new O,this.onMenuOpen=this.menuOpened,this.menuClosed=new O,this.onMenuClose=this.menuClosed,this._scrollStrategy=n,this._parentMaterialMenu=s instanceof z?s:void 0,t.nativeElement.addEventListener("touchstart",this._handleTouchStart,Be)}ngAfterContentInit(){this._handleHover()}ngOnDestroy(){this._overlayRef&&(this._overlayRef.dispose(),this._overlayRef=null),this._element.nativeElement.removeEventListener("touchstart",this._handleTouchStart,Be),this._menuCloseSubscription.unsubscribe(),this._closingActionsSubscription.unsubscribe(),this._hoverSubscription.unsubscribe()}get menuOpen(){return this._menuOpen}get dir(){return this._dir&&this._dir.value==="rtl"?"rtl":"ltr"}triggersSubmenu(){return!!(this._menuItemInstance&&this._parentMaterialMenu&&this.menu)}toggleMenu(){return this._menuOpen?this.closeMenu():this.openMenu()}openMenu(){let e=this.menu;if(this._menuOpen||!e)return;let t=this._createOverlay(e),i=t.getConfig(),n=i.positionStrategy;this._setPosition(e,n),i.hasBackdrop=e.hasBackdrop==null?!this.triggersSubmenu():e.hasBackdrop,t.attach(this._getPortal(e)),e.lazyContent&&e.lazyContent.attach(this.menuData),this._closingActionsSubscription=this._menuClosingActions().subscribe(()=>this.closeMenu()),this._initMenu(e),e instanceof z&&(e._startAnimation(),e._directDescendantItems.changes.pipe(x(e.close)).subscribe(()=>{n.withLockedPosition(!1).reapplyLastPosition(),n.withLockedPosition(!0)}))}closeMenu(){this.menu?.close.emit()}focus(e,t){this._focusMonitor&&e?this._focusMonitor.focusVia(this._element,e,t):this._element.nativeElement.focus(t)}updatePosition(){this._overlayRef?.updatePosition()}_destroyMenu(e){if(!this._overlayRef||!this.menuOpen)return;let t=this.menu;this._closingActionsSubscription.unsubscribe(),this._overlayRef.detach(),this.restoreFocus&&(e==="keydown"||!this._openedBy||!this.triggersSubmenu())&&this.focus(this._openedBy),this._openedBy=void 0,t instanceof z?(t._resetAnimation(),t.lazyContent?t._animationDone.pipe(g(i=>i.toState==="void"),S(1),x(t.lazyContent._attached)).subscribe({next:()=>t.lazyContent.detach(),complete:()=>this._setIsMenuOpen(!1)}):this._setIsMenuOpen(!1)):(this._setIsMenuOpen(!1),t?.lazyContent?.detach())}_initMenu(e){e.parentMenu=this.triggersSubmenu()?this._parentMaterialMenu:void 0,e.direction=this.dir,this._setMenuElevation(e),e.focusFirstItem(this._openedBy||"program"),this._setIsMenuOpen(!0)}_setMenuElevation(e){if(e.setElevation){let t=0,i=e.parentMenu;for(;i;)t++,i=i.parentMenu;e.setElevation(t)}}_setIsMenuOpen(e){e!==this._menuOpen&&(this._menuOpen=e,this._menuOpen?this.menuOpened.emit():this.menuClosed.emit(),this.triggersSubmenu()&&this._menuItemInstance._setHighlighted(e),this._changeDetectorRef.markForCheck())}_createOverlay(e){if(!this._overlayRef){let t=this._getOverlayConfig(e);this._subscribeToPositions(e,t.positionStrategy),this._overlayRef=this._overlay.create(t),this._overlayRef.keydownEvents().subscribe()}return this._overlayRef}_getOverlayConfig(e){return new je({positionStrategy:this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn(".mat-menu-panel, .mat-mdc-menu-panel"),backdropClass:e.backdropClass||"cdk-overlay-transparent-backdrop",panelClass:e.overlayPanelClass,scrollStrategy:this._scrollStrategy(),direction:this._dir})}_subscribeToPositions(e,t){e.setPositionClasses&&t.positionChanges.subscribe(i=>{let n=i.connectionPair.overlayX==="start"?"after":"before",s=i.connectionPair.overlayY==="top"?"below":"above";this._ngZone?this._ngZone.run(()=>e.setPositionClasses(n,s)):e.setPositionClasses(n,s)})}_setPosition(e,t){let[i,n]=e.xPosition==="before"?["end","start"]:["start","end"],[s,m]=e.yPosition==="above"?["bottom","top"]:["top","bottom"],[u,d]=[s,m],[h,p]=[i,n],_=0;if(this.triggersSubmenu()){if(p=i=e.xPosition==="before"?"start":"end",n=h=i==="end"?"start":"end",this._parentMaterialMenu){if(this._parentInnerPadding==null){let ue=this._parentMaterialMenu.items.first;this._parentInnerPadding=ue?ue._getHostElement().offsetTop:0}_=s==="bottom"?this._parentInnerPadding:-this._parentInnerPadding}}else e.overlapTrigger||(u=s==="top"?"bottom":"top",d=m==="top"?"bottom":"top");t.withPositions([{originX:i,originY:u,overlayX:h,overlayY:s,offsetY:_},{originX:n,originY:u,overlayX:p,overlayY:s,offsetY:_},{originX:i,originY:d,overlayX:h,overlayY:m,offsetY:-_},{originX:n,originY:d,overlayX:p,overlayY:m,offsetY:-_}])}_menuClosingActions(){let e=this._overlayRef.backdropClick(),t=this._overlayRef.detachments(),i=this._parentMaterialMenu?this._parentMaterialMenu.closed:Q(),n=this._parentMaterialMenu?this._parentMaterialMenu._hovered().pipe(g(s=>s!==this._menuItemInstance),g(()=>this._menuOpen)):Q();return E(e,i,n,t)}_handleMousedown(e){Ee(e)||(this._openedBy=e.button===0?"mouse":void 0,this.triggersSubmenu()&&e.preventDefault())}_handleKeydown(e){let t=e.keyCode;(t===13||t===32)&&(this._openedBy="keyboard"),this.triggersSubmenu()&&(t===39&&this.dir==="ltr"||t===37&&this.dir==="rtl")&&(this._openedBy="keyboard",this.openMenu())}_handleClick(e){this.triggersSubmenu()?(e.stopPropagation(),this.openMenu()):this.toggleMenu()}_handleHover(){!this.triggersSubmenu()||!this._parentMaterialMenu||(this._hoverSubscription=this._parentMaterialMenu._hovered().pipe(g(e=>e===this._menuItemInstance&&!e.disabled),X(0,U)).subscribe(()=>{this._openedBy="mouse",this.menu instanceof z&&this.menu._isAnimating?this.menu._animationDone.pipe(S(1),X(0,U),x(this._parentMaterialMenu._hovered())).subscribe(()=>this.openMenu()):this.openMenu()}))}_getPortal(e){return(!this._portal||this._portal.templateRef!==e.templateRef)&&(this._portal=new Le(e.templateRef,this._viewContainerRef)),this._portal}static{this.\u0275fac=function(t){return new(t||o)(a(R),a(F),a(ve),a(ze),a(ce,8),a(le,10),a(Ae,8),a(re),a($))}}static{this.\u0275dir=_e({type:o,selectors:[["","mat-menu-trigger-for",""],["","matMenuTriggerFor",""]],hostAttrs:[1,"mat-mdc-menu-trigger"],hostVars:3,hostBindings:function(t,i){t&1&&j("click",function(s){return i._handleClick(s)})("mousedown",function(s){return i._handleMousedown(s)})("keydown",function(s){return i._handleKeydown(s)}),t&2&&M("aria-haspopup",i.menu?"menu":null)("aria-expanded",i.menuOpen)("aria-controls",i.menuOpen?i.menu.panelId:null)},inputs:{_deprecatedMatMenuTriggerFor:[r.None,"mat-menu-trigger-for","_deprecatedMatMenuTriggerFor"],menu:[r.None,"matMenuTriggerFor","menu"],menuData:[r.None,"matMenuTriggerData","menuData"],restoreFocus:[r.None,"matMenuTriggerRestoreFocus","restoreFocus"]},outputs:{menuOpened:"menuOpened",onMenuOpen:"onMenuOpen",menuClosed:"menuClosed",onMenuClose:"onMenuClose"},exportAs:["matMenuTrigger"],standalone:!0})}}return o})(),Vt=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=pe({type:o})}static{this.\u0275inj=he({providers:[at],imports:[Re,Fe,me,Ne,Oe,me]})}}return o})();export{le as a,z as b,zt as c,Vt as d};
